#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java用户适配器
将Java系统的LoginUser对象转换为FastAPI系统的用户对象
"""
import json
import time
from typing import Optional, Dict, Any, List
from datetime import datetime

from backend.app.admin.schema.user import GetUserInfoWithRelationDetail
from backend.app.admin.schema.role import GetRoleWithRelationDetail
from backend.app.admin.schema.menu import GetMenuDetail
from backend.common.log import log


class JavaUserAdapter:
    """Java用户适配器"""
    
    @staticmethod
    def build_fastapi_user(login_user: Dict[str, Any]) -> GetUserInfoWithRelationDetail:
        """
        将Java LoginUser对象转换为FastAPI用户对象
        
        :param login_user: Java系统的LoginUser对象
        :return: FastAPI用户对象
        """
        try:
            java_user = login_user.get('user', {})
            
            # 构造基本用户信息
            import uuid
            user_info = GetUserInfoWithRelationDetail(
                id=java_user.get('userId', 0),
                uuid=str(uuid.uuid4()),  # 生成新的UUID
                username=java_user.get('userName', ''),
                nickname=java_user.get('nickName', ''),
                email=java_user.get('email', ''),
                phone=java_user.get('phonenumber', ''),
                avatar=java_user.get('avatar') or None,  # 空字符串转为None
                is_superuser=java_user.get('admin', False),
                is_staff=True,  # Java系统登录用户默认为staff
                is_multi_login=True,  # 默认允许多点登录
                status=1 if java_user.get('status') == '0' else 0,  # Java: '0'=正常, '1'=停用
                dept_id=java_user.get('deptId'),
                join_time=JavaUserAdapter._parse_java_datetime(java_user.get('createTime')) or datetime.now(),
                create_time=JavaUserAdapter._parse_java_datetime(java_user.get('createTime')),
                update_time=JavaUserAdapter._parse_java_datetime(java_user.get('updateTime')),
                # 权限和角色信息
                roles=JavaUserAdapter._build_roles(login_user),
                permissions=JavaUserAdapter._build_permissions(login_user),
                # 部门信息
                dept=JavaUserAdapter._build_dept(java_user.get('dept', {}))
            )
            
            log.debug(f"Java用户适配成功: {user_info.username} (ID: {user_info.id})")
            return user_info
            
        except Exception as e:
            log.error(f"Java用户适配失败: {str(e)}")
            log.error(f"原始LoginUser数据: {login_user}")
            raise ValueError(f"Java用户适配失败: {str(e)}")
    
    @staticmethod
    def _build_roles(login_user: Dict[str, Any]) -> List[GetRoleWithRelationDetail]:
        """
        构造角色信息
        
        :param login_user: LoginUser对象
        :return: 角色列表
        """
        try:
            java_roles = login_user.get('roles', [])
            java_user = login_user.get('user', {})
            user_roles = java_user.get('roles', [])
            
            # 合并两个来源的角色信息
            all_roles = []
            
            # 处理login_user.roles（字符串数组）
            if isinstance(java_roles, list):
                for i, role_name in enumerate(java_roles):
                    if isinstance(role_name, str):
                        from backend.common.enums import StatusType
                        all_roles.append(GetRoleWithRelationDetail(
                            id=i + 1000,  # 使用偏移避免ID冲突
                            name=role_name,
                            status=StatusType.enable,  # 默认启用
                            is_filter_scopes=True,
                            remark=f"Java系统角色: {role_name}",
                            created_time=datetime.now(),
                            menus=[],  # 菜单信息后续填充
                            scopes=[]  # 数据范围后续填充
                        ))

            # 处理user.roles（对象数组）
            if isinstance(user_roles, list):
                for role_obj in user_roles:
                    if isinstance(role_obj, dict):
                        from backend.common.enums import StatusType
                        all_roles.append(GetRoleWithRelationDetail(
                            id=role_obj.get('roleId', 0),
                            name=role_obj.get('roleName', ''),
                            status=StatusType.enable if role_obj.get('status') == '0' else StatusType.disable,
                            is_filter_scopes=True,
                            remark=role_obj.get('remark', ''),
                            created_time=datetime.now(),
                            menus=[],  # 菜单信息需要单独查询
                            scopes=[]  # 数据范围需要单独查询
                        ))
            
            return all_roles
            
        except Exception as e:
            log.warning(f"构造角色信息失败: {str(e)}")
            return []
    
    @staticmethod
    def _build_permissions(login_user: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        构造权限信息
        
        :param login_user: LoginUser对象
        :return: 权限列表
        """
        try:
            java_permissions = login_user.get('permissions', [])
            
            if not isinstance(java_permissions, list):
                return []
            
            permissions = []
            for perm in java_permissions:
                if isinstance(perm, str) and perm.strip():
                    permissions.append({'name': perm.strip()})
            
            return permissions
            
        except Exception as e:
            log.warning(f"构造权限信息失败: {str(e)}")
            return []
    
    @staticmethod
    def _build_dept(dept_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        构造部门信息

        :param dept_data: Java部门数据
        :return: 部门信息
        """
        try:
            if not dept_data:
                return None

            return {
                'id': dept_data.get('deptId', 0),
                'name': dept_data.get('deptName', ''),
                'parent_id': dept_data.get('parentId', 0),
                'sort': dept_data.get('orderNum', 0),
                'status': 1 if dept_data.get('status') == '0' else 0,
                'leader': dept_data.get('leader', ''),
                'phone': dept_data.get('phone') or '15888888888',  # 提供默认手机号
                'email': dept_data.get('email', ''),
                'del_flag': False,  # 默认未删除
                'created_time': datetime.now()  # 默认创建时间
            }

        except Exception as e:
            log.warning(f"构造部门信息失败: {str(e)}")
            return None
    
    @staticmethod
    def _parse_java_datetime(datetime_str: Optional[str]) -> Optional[datetime]:
        """
        解析Java系统的日期时间字符串
        
        :param datetime_str: Java日期时间字符串
        :return: Python datetime对象
        """
        if not datetime_str:
            return None
            
        try:
            # Java格式: "2024-12-05T16:00:00.000+08:00"
            if 'T' in datetime_str:
                # 移除时区信息进行简单解析
                clean_str = datetime_str.split('+')[0].split('-')[0:3]
                clean_str = '-'.join(clean_str[:3]) + 'T' + datetime_str.split('T')[1].split('+')[0].split('.')[0]
                return datetime.fromisoformat(clean_str)
            else:
                # 尝试其他格式
                return datetime.fromisoformat(datetime_str)
                
        except Exception as e:
            log.warning(f"解析Java日期时间失败: {datetime_str}, 错误: {str(e)}")
            return None
    
    @staticmethod
    def validate_login_user(login_user: Dict[str, Any]) -> bool:
        """
        验证LoginUser对象的有效性
        
        :param login_user: LoginUser对象
        :return: 是否有效
        """
        try:
            # 检查必要字段
            if not login_user.get('token'):
                log.warning("LoginUser缺少token字段")
                return False
                
            if not login_user.get('user'):
                log.warning("LoginUser缺少user字段")
                return False
                
            user = login_user['user']
            if not user.get('userId'):
                log.warning("LoginUser.user缺少userId字段")
                return False
                
            if not user.get('userName'):
                log.warning("LoginUser.user缺少userName字段")
                return False
            
            # 检查过期时间
            expire_time = login_user.get('expireTime', 0)
            if expire_time and expire_time < int(time.time() * 1000):
                log.warning(f"LoginUser已过期: {expire_time}")
                return False
            
            return True
            
        except Exception as e:
            log.error(f"验证LoginUser失败: {str(e)}")
            return False
    
    @staticmethod
    def extract_user_permissions(login_user: Dict[str, Any]) -> List[str]:
        """
        提取用户权限列表（用于权限验证）
        
        :param login_user: LoginUser对象
        :return: 权限字符串列表
        """
        try:
            permissions = login_user.get('permissions', [])
            if isinstance(permissions, list):
                return [perm for perm in permissions if isinstance(perm, str)]
            return []
            
        except Exception as e:
            log.warning(f"提取用户权限失败: {str(e)}")
            return []
    
    @staticmethod
    def extract_user_roles(login_user: Dict[str, Any]) -> List[str]:
        """
        提取用户角色列表
        
        :param login_user: LoginUser对象
        :return: 角色字符串列表
        """
        try:
            roles = login_user.get('roles', [])
            if isinstance(roles, list):
                return [role for role in roles if isinstance(role, str)]
            return []
            
        except Exception as e:
            log.warning(f"提取用户角色失败: {str(e)}")
            return []
