2025-08-05 08:03:44.090 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\start_stable.py", line 12, in <module>
    uvicorn.run(
    │       └ <function run at 0x0000022A22138AE0>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000022A2213B9C0>
    └ <uvicorn.server.Server object at 0x0000022A289B2B40>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000022A2213BA60>
           │       │   └ <uvicorn.server.Server object at 0x0000022A289B2B40>
           │       └ <function run at 0x0000022A2196AFC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000022A28B5CD60>
           │      └ <function Runner.run at 0x0000022A21A07240>
           └ <asyncio.runners.Runner object at 0x0000022A28675520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000022A21A04E00>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000022A28675520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000022A21AD8CC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000022A21A06B60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000022A219647C0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1652, family=2, type=1, proto=6, laddr=('*************', 61347), raddr=('*************', 5981)>
    └ <_ProactorSocketTransport closing fd=1652>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-05 08:03:44.102 | ERROR    | c28c8b8480724cb4864d99d2ef6cd02b | 请求异常: 请求参数非法: func 字段为必填项，输入：None
2025-08-05 08:37:02.598 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to *************:6379. 信号灯超时时间已到.
2025-08-05 08:38:34.096 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 08:39:43.586 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 08:56:28.920 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 08:57:22.287 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 08:59:21.874 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 09:01:08.035 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 09:02:31.717 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to *************:6379. 信号灯超时时间已到.
2025-08-05 09:03:59.541 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 09:46:57.030 | ERROR    | b67fa89f46234019b18a2ba4ec0a3da4 | 请求异常: object ResponseModel can't be used in 'await' expression
2025-08-05 09:47:32.100 | ERROR    | 055a85f0decb43578e94199ca138af62 | 请求异常: object ResponseModel can't be used in 'await' expression
2025-08-05 09:57:34.851 | ERROR    | 31741dc528e24a4b9987c2dd08411805 | 请求异常: Not Authenticated
2025-08-05 09:57:34.968 | ERROR    | fef47d83558045c5b119e03c92d91756 | 请求异常: Not Authenticated
2025-08-05 09:57:52.082 | ERROR    | b4b961a36bf541fd9a13c76cd24d1161 | 请求异常: Not Authenticated
2025-08-05 09:57:52.371 | ERROR    | a79eab72ff524b33a2d65ad8d33c5d35 | 请求异常: Not Authenticated
2025-08-05 09:58:09.746 | ERROR    | 31159c9b83e04161be1e28f5abc8190b | 请求异常: Not Authenticated
2025-08-05 09:58:09.761 | ERROR    | 57f2d030531240eb89f776bb5c48b93d | 请求异常: Not Authenticated
2025-08-05 09:58:57.503 | ERROR    | c7bd2cc1b5624666932a04745cef7434 | 请求异常: Not Authenticated
2025-08-05 09:58:57.530 | ERROR    | a03c028c84a24ae3a516270d08895153 | 请求异常: Not Authenticated
2025-08-05 10:01:37.464 | ERROR    | 046c18fd739047c89dd3d7735aa412f0 | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'msg'
2025-08-05 10:03:14.335 | ERROR    | 9ef1d68ffc2149e99d12b70fb5ebf6bc | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'msg'
2025-08-05 10:25:46.542 | ERROR    | a9feb53621b7491d9d0419edbba4546f | Java token验证异常: cannot import name 'GetRoleListDetails' from 'backend.app.admin.schema.role' (C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\admin\schema\role.py)
2025-08-05 10:25:46.543 | ERROR    | a9feb53621b7491d9d0419edbba4546f | 异常堆栈: Traceback (most recent call last):
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 115, in _authenticate_java_token
    from backend.common.security.java_user_adapter import JavaUserAdapter
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_user_adapter.py", line 13, in <module>
    from backend.app.admin.schema.role import GetRoleListDetails
ImportError: cannot import name 'GetRoleListDetails' from 'backend.app.admin.schema.role' (C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\admin\schema\role.py)

2025-08-05 10:27:16.332 | ERROR    | 1edbe593c2c04e30898de8caad22c669 | Java token验证异常: No module named 'backend.database.db_redis'
2025-08-05 10:27:16.332 | ERROR    | 1edbe593c2c04e30898de8caad22c669 | 异常堆栈: Traceback (most recent call last):
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 116, in _authenticate_java_token
    from backend.database.db_redis import redis_client
ModuleNotFoundError: No module named 'backend.database.db_redis'

2025-08-05 10:28:07.697 | ERROR    | f864ccd8b9c54020912d7c4be9212c4e | Java token验证异常: No module named 'backend.database.db_redis'
2025-08-05 10:28:07.698 | ERROR    | f864ccd8b9c54020912d7c4be9212c4e | 异常堆栈: Traceback (most recent call last):
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 116, in _authenticate_java_token
    from backend.database.redis import redis_client
ModuleNotFoundError: No module named 'backend.database.db_redis'

2025-08-05 10:30:14.521 | ERROR    | ea7350d2c9e5481096868197971457e3 | Java用户适配失败: 7 validation errors for GetUserInfoWithRelationDetail
avatar
  Input should be a valid URL, input is empty [type=url_parsing, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/url_parsing
uuid
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
is_multi_login
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
join_time
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.phone
  String should match pattern '^1[3-9]\d{9}$' [type=string_pattern_mismatch, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/string_pattern_mismatch
dept.del_flag
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.created_time
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-05 10:30:14.522 | ERROR    | ea7350d2c9e5481096868197971457e3 | 原始LoginUser数据: {'@type': 'com.fastbee.common.core.domain.model.LoginUser', 'browser': 'Chrome 13', 'deptId': 103, 'expireTime': 1754366177106, 'ipaddr': '*************', 'loginLocation': '内网IP', 'loginTime': 1754279777106, 'neverExpire': False, 'os': 'Windows 10', 'permissions': ['*:*:*'], 'token': '457b5f2c-6482-47c3-b22c-0cf6a5cf3508', 'user': {'admin': True, 'avatar': '', 'createBy': 'admin', 'createTime': '2021-12-15 21:36:18', 'delFlag': '0', 'dept': {'ancestors': '0,100,101', 'children': [], 'deptId': 103, 'deptName': '研发部门', 'leader': '物美', 'orderNum': 1, 'params': {'@type': 'java.util.HashMap'}, 'parentId': 101, 'status': '1'}, 'deptId': 103, 'email': '<EMAIL>', 'loginDate': '2025-08-04 11:54:03', 'loginIp': '*************', 'nickName': '蜂信管理员', 'params': {'@type': 'java.util.HashMap'}, 'password': '$2a$10$VJgxhCwmqjO69RXPtQPbxu8YIJ3rdA89004FVJf3Z9tKJxRGjQ4Nu', 'phonenumber': '15888888888', 'remark': '管理员', 'roles': [{'admin': True, 'dataScope': '1', 'deptCheckStrictly': False, 'flag': False, 'menuCheckStrictly': False, 'params': {'@type': 'java.util.HashMap'}, 'roleId': 1, 'roleKey': 'admin', 'roleName': '超级管理员', 'roleSort': 1, 'status': '0'}], 'sex': '0', 'status': '0', 'userId': 1, 'userName': 'admin'}, 'userId': 1, 'username': 'admin'}
2025-08-05 10:30:14.522 | ERROR    | ea7350d2c9e5481096868197971457e3 | Java token验证异常: Java用户适配失败: 7 validation errors for GetUserInfoWithRelationDetail
avatar
  Input should be a valid URL, input is empty [type=url_parsing, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/url_parsing
uuid
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
is_multi_login
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
join_time
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.phone
  String should match pattern '^1[3-9]\d{9}$' [type=string_pattern_mismatch, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/string_pattern_mismatch
dept.del_flag
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.created_time
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-05 10:30:14.523 | ERROR    | ea7350d2c9e5481096868197971457e3 | 异常堆栈: Traceback (most recent call last):
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_user_adapter.py", line 33, in build_fastapi_user
    user_info = GetUserInfoWithRelationDetail(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 7 validation errors for GetUserInfoWithRelationDetail
avatar
  Input should be a valid URL, input is empty [type=url_parsing, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/url_parsing
uuid
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
is_multi_login
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
join_time
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.phone
  String should match pattern '^1[3-9]\d{9}$' [type=string_pattern_mismatch, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/string_pattern_mismatch
dept.del_flag
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.created_time
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 152, in _authenticate_java_token
    fastapi_user = JavaUserAdapter.build_fastapi_user(login_user)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_user_adapter.py", line 59, in build_fastapi_user
    raise ValueError(f"Java用户适配失败: {str(e)}")
ValueError: Java用户适配失败: 7 validation errors for GetUserInfoWithRelationDetail
avatar
  Input should be a valid URL, input is empty [type=url_parsing, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/url_parsing
uuid
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
is_multi_login
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
join_time
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.phone
  String should match pattern '^1[3-9]\d{9}$' [type=string_pattern_mismatch, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/string_pattern_mismatch
dept.del_flag
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.created_time
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing

2025-08-05 10:31:47.374 | ERROR    | dd4b287dd88a423b81066a104f9c2300 | Java用户适配失败: 7 validation errors for GetUserInfoWithRelationDetail
avatar
  Input should be a valid URL, input is empty [type=url_parsing, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/url_parsing
uuid
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
is_multi_login
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
join_time
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.phone
  String should match pattern '^1[3-9]\d{9}$' [type=string_pattern_mismatch, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/string_pattern_mismatch
dept.del_flag
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.created_time
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-05 10:31:47.374 | ERROR    | dd4b287dd88a423b81066a104f9c2300 | 原始LoginUser数据: {'@type': 'com.fastbee.common.core.domain.model.LoginUser', 'browser': 'Chrome 13', 'deptId': 103, 'expireTime': 1754366177106, 'ipaddr': '*************', 'loginLocation': '内网IP', 'loginTime': 1754279777106, 'neverExpire': False, 'os': 'Windows 10', 'permissions': ['*:*:*'], 'token': '457b5f2c-6482-47c3-b22c-0cf6a5cf3508', 'user': {'admin': True, 'avatar': '', 'createBy': 'admin', 'createTime': '2021-12-15 21:36:18', 'delFlag': '0', 'dept': {'ancestors': '0,100,101', 'children': [], 'deptId': 103, 'deptName': '研发部门', 'leader': '物美', 'orderNum': 1, 'params': {'@type': 'java.util.HashMap'}, 'parentId': 101, 'status': '1'}, 'deptId': 103, 'email': '<EMAIL>', 'loginDate': '2025-08-04 11:54:03', 'loginIp': '*************', 'nickName': '蜂信管理员', 'params': {'@type': 'java.util.HashMap'}, 'password': '$2a$10$VJgxhCwmqjO69RXPtQPbxu8YIJ3rdA89004FVJf3Z9tKJxRGjQ4Nu', 'phonenumber': '15888888888', 'remark': '管理员', 'roles': [{'admin': True, 'dataScope': '1', 'deptCheckStrictly': False, 'flag': False, 'menuCheckStrictly': False, 'params': {'@type': 'java.util.HashMap'}, 'roleId': 1, 'roleKey': 'admin', 'roleName': '超级管理员', 'roleSort': 1, 'status': '0'}], 'sex': '0', 'status': '0', 'userId': 1, 'userName': 'admin'}, 'userId': 1, 'username': 'admin'}
2025-08-05 10:31:47.374 | ERROR    | dd4b287dd88a423b81066a104f9c2300 | Java token验证异常: Java用户适配失败: 7 validation errors for GetUserInfoWithRelationDetail
avatar
  Input should be a valid URL, input is empty [type=url_parsing, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/url_parsing
uuid
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
is_multi_login
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
join_time
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.phone
  String should match pattern '^1[3-9]\d{9}$' [type=string_pattern_mismatch, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/string_pattern_mismatch
dept.del_flag
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.created_time
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-05 10:31:47.375 | ERROR    | dd4b287dd88a423b81066a104f9c2300 | 异常堆栈: Traceback (most recent call last):
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_user_adapter.py", line 33, in build_fastapi_user
    import uuid
                
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 7 validation errors for GetUserInfoWithRelationDetail
avatar
  Input should be a valid URL, input is empty [type=url_parsing, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/url_parsing
uuid
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
is_multi_login
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
join_time
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.phone
  String should match pattern '^1[3-9]\d{9}$' [type=string_pattern_mismatch, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/string_pattern_mismatch
dept.del_flag
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.created_time
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 152, in _authenticate_java_token
    fastapi_user = JavaUserAdapter.build_fastapi_user(login_user)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_user_adapter.py", line 59, in build_fastapi_user
ValueError: Java用户适配失败: 7 validation errors for GetUserInfoWithRelationDetail
avatar
  Input should be a valid URL, input is empty [type=url_parsing, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/url_parsing
uuid
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
is_multi_login
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
join_time
  Field required [type=missing, input_value={'id': 1, 'username': 'ad...hone': '', 'email': ''}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.phone
  String should match pattern '^1[3-9]\d{9}$' [type=string_pattern_mismatch, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/string_pattern_mismatch
dept.del_flag
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
dept.created_time
  Field required [type=missing, input_value={'id': 103, 'name': '研...phone': '', 'email': ''}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing

2025-08-05 10:40:02.795 | ERROR    | b631f380d89b4db2a7c46606c494295b | 请求异常: 请求参数非法: func 字段为必填项，输入：None
2025-08-05 10:42:23.461 | ERROR    | a23a35358fb14f2c93e676abc92de627 | 请求异常: 请求参数非法: func 字段为必填项，输入：None
2025-08-05 10:44:48.857 | ERROR    | 6ee75074cab548beae00a8448604e073 | 请求异常: Not Authenticated
2025-08-05 10:44:48.859 | ERROR    | 9cce1d05ff2c421ea538aa7f0d2169a3 | 请求异常: Not Authenticated
2025-08-05 10:44:51.566 | ERROR    | fcfea0835b324cf3ad8d2a934d95ff20 | 请求异常: Not Authenticated
2025-08-05 10:44:56.220 | ERROR    | 777e015e8df148bebe79917b9b1f5586 | 请求异常: Not Authenticated
2025-08-05 10:44:57.647 | ERROR    | ff95e64cda3947a0af561366f467f6f4 | 请求异常: Not Authenticated
2025-08-05 10:56:10.956 | ERROR    | 8b12ea89319240da8f4b13811e821ff8 | 请求异常: Not Authenticated
2025-08-05 10:56:25.344 | ERROR    | 2a01d3944e4442c49047048c72f5b438 | 请求异常: Not Found
2025-08-05 11:02:29.469 | ERROR    | 4d75ae428b3742aeba93f219486aa949 | 请求异常: 请求参数非法: func 字段为必填项，输入：None
2025-08-05 11:02:33.952 | ERROR    | 1f0c1d4479ed4be88e7bb97cc2a305e5 | 请求异常: 请求参数非法: func 字段为必填项，输入：None
2025-08-05 11:02:36.971 | ERROR    | 0937d4f19fbb4f93a86ce9e83bc9979e | 请求异常: 请求参数非法: func 字段为必填项，输入：None
2025-08-05 11:13:06.412 | ERROR    | 8e4612c500d347c089718e6dadbb2df6 | 请求异常: Not Authenticated
2025-08-05 11:18:41.722 | ERROR    | caf263ec8729473792c9e121cc6f1aec | 请求异常: Not Authenticated
2025-08-05 11:18:56.162 | ERROR    | cfc94c552fde4c519be7ff26527fdc4b | 请求异常: Not Found
