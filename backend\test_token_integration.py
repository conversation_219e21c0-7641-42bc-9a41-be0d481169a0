#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token自动设置功能集成测试
验证前端token自动获取和API调用是否正常工作
"""
import json
import urllib.request
import urllib.parse
import urllib.error
import time
from typing import Dict, List, Optional

class TokenIntegrationTester:
    """Token集成测试类"""
    
    def __init__(self):
        self.frontend_url = "http://localhost:3000"  # 前端开发服务器地址
        self.backend_url = "http://localhost:8000"   # 后端API地址
        self.test_results = []
        
        # 测试用的token（admin用户）
        self.admin_jwt_token = "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjQ1N2I1ZjJjLTY0ODItNDdjMy1iMjJjLTBjZjZhNWNmMzUwOCJ9.YEfuSy_rl2g4_9rbSd4A34qtatSWhOBfMe-Rzw0c3McGJQjMBIUs_9VWxEx8inrDU5asAtVTYTyl0m6bVmYfzw"
        
    def make_request(self, method: str, url: str, token: str = None, data: dict = None) -> Dict:
        """发起HTTP请求"""
        try:
            # 构建请求
            req = urllib.request.Request(url)
            req.add_header("Content-Type", "application/json")
            
            if token:
                req.add_header("Authorization", f"Bearer {token}")
            
            if method.upper() == "POST" and data:
                req.data = json.dumps(data).encode('utf-8')
                req.get_method = lambda: 'POST'
            
            # 发送请求
            with urllib.request.urlopen(req, timeout=10) as response:
                result = json.loads(response.read().decode('utf-8'))
                return {
                    "status": response.getcode(),
                    "data": result,
                    "success": response.getcode() < 400
                }
                
        except urllib.error.HTTPError as e:
            try:
                result = json.loads(e.read().decode('utf-8'))
            except:
                result = {"error": str(e)}
            return {
                "status": e.code,
                "data": result,
                "success": False
            }
        except Exception as e:
            return {
                "status": 0,
                "data": {"error": str(e)},
                "success": False
            }
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": time.time()
        })
    
    def test_backend_health(self):
        """测试后端健康状态"""
        print("\n🔍 测试1: 后端服务健康检查")
        
        # 无token访问
        result = self.make_request("GET", f"{self.backend_url}/api/v1/iot/kb/health")
        self.log_test(
            "后端健康检查 (无token)", 
            result["success"] and result["data"]["code"] == 200,
            f"状态码: {result['status']}, 响应: {result['data'].get('msg', '')}"
        )
        
        # 有token访问
        result = self.make_request("GET", f"{self.backend_url}/api/v1/iot/kb/health", self.admin_jwt_token)
        self.log_test(
            "后端健康检查 (有token)", 
            result["success"] and result["data"]["code"] == 200,
            f"状态码: {result['status']}, 响应: {result['data'].get('msg', '')}"
        )
    
    def test_backend_authentication(self):
        """测试后端认证功能"""
        print("\n🔐 测试2: 后端JWT认证功能")
        
        # 无token访问需要认证的接口
        result = self.make_request("GET", f"{self.backend_url}/api/v1/iot/kb/datasets")
        self.log_test(
            "无token访问受保护接口", 
            not result["success"] and result["status"] == 401,
            f"状态码: {result['status']} (期望401)"
        )
        
        # 有效token访问
        result = self.make_request("GET", f"{self.backend_url}/api/v1/iot/kb/datasets", self.admin_jwt_token)
        self.log_test(
            "有效token访问受保护接口", 
            result["success"] and result["status"] == 200,
            f"状态码: {result['status']}, 数据: {len(result['data'].get('data', []))} 条记录"
        )
    
    def test_frontend_proxy(self):
        """测试前端代理功能"""
        print("\n🌐 测试3: 前端代理功能")
        
        # 测试通过前端代理访问后端API
        try:
            # 这里需要前端服务器运行在localhost:3000
            proxy_url = f"{self.frontend_url}/fastapi/api/v1/iot/kb/health"
            result = self.make_request("GET", proxy_url)
            self.log_test(
                "前端代理健康检查", 
                result["success"],
                f"状态码: {result['status']}, 代理工作正常"
            )
        except Exception as e:
            self.log_test(
                "前端代理健康检查", 
                False,
                f"代理测试失败: {str(e)} (请确保前端服务器运行在 {self.frontend_url})"
            )
    
    def test_token_storage_simulation(self):
        """模拟token存储测试"""
        print("\n💾 测试4: Token存储机制模拟")
        
        # 模拟不同的token存储场景
        test_scenarios = [
            {
                "name": "Cookie中有token",
                "cookie": self.admin_jwt_token,
                "session": None,
                "local": None,
                "expected": self.admin_jwt_token
            },
            {
                "name": "SessionStorage中有token",
                "cookie": None,
                "session": self.admin_jwt_token,
                "local": None,
                "expected": self.admin_jwt_token
            },
            {
                "name": "LocalStorage中有token",
                "cookie": None,
                "session": None,
                "local": self.admin_jwt_token,
                "expected": self.admin_jwt_token
            },
            {
                "name": "所有位置都没有token",
                "cookie": None,
                "session": None,
                "local": None,
                "expected": None
            }
        ]
        
        for scenario in test_scenarios:
            # 模拟token获取逻辑
            token = scenario["cookie"] or scenario["session"] or scenario["local"]
            expected = scenario["expected"]
            
            success = (token == expected)
            self.log_test(
                f"Token获取逻辑 - {scenario['name']}", 
                success,
                f"获取到: {token[:20] + '...' if token else 'None'}, 期望: {expected[:20] + '...' if expected else 'None'}"
            )
    
    def test_api_integration(self):
        """测试API集成"""
        print("\n🔗 测试5: API集成测试")
        
        # 测试主要的知识库API接口
        api_tests = [
            {
                "name": "知识库列表接口",
                "url": f"{self.backend_url}/api/v1/iot/kb/datasets",
                "method": "GET",
                "need_auth": True
            },
            {
                "name": "统计信息接口",
                "url": f"{self.backend_url}/api/v1/iot/kb/datasets/stats/summary",
                "method": "GET",
                "need_auth": True
            },
            {
                "name": "健康检查接口",
                "url": f"{self.backend_url}/api/v1/iot/kb/health",
                "method": "GET",
                "need_auth": False
            }
        ]
        
        for api_test in api_tests:
            token = self.admin_jwt_token if api_test["need_auth"] else None
            result = self.make_request(api_test["method"], api_test["url"], token)
            
            expected_success = True
            if api_test["need_auth"] and not token:
                expected_success = False
            
            success = result["success"] == expected_success
            self.log_test(
                f"API测试 - {api_test['name']}", 
                success,
                f"状态码: {result['status']}, 认证: {'需要' if api_test['need_auth'] else '不需要'}"
            )
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 Token自动设置功能集成测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['details']}")
        
        print("\n📋 测试建议:")
        if passed_tests == total_tests:
            print("✅ 所有测试通过！Token自动设置功能工作正常。")
            print("✅ 可以进行前端页面测试，用户登录后应该能直接使用知识库功能。")
        else:
            print("⚠️ 部分测试失败，请检查以下问题：")
            print("   1. 确保FastAPI后端服务运行在 http://localhost:8000")
            print("   2. 确保前端开发服务器运行在 http://localhost:3000")
            print("   3. 检查Vite代理配置是否正确")
            print("   4. 验证JWT token是否有效")
        
        return failed_tests == 0
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始Token自动设置功能集成测试")
        print("=" * 60)
        
        start_time = time.time()
        
        # 运行所有测试
        self.test_backend_health()
        self.test_backend_authentication()
        self.test_frontend_proxy()
        self.test_token_storage_simulation()
        self.test_api_integration()
        
        # 生成报告
        end_time = time.time()
        success = self.generate_test_report()
        
        print(f"\n⏱️ 测试耗时: {end_time-start_time:.2f}秒")
        
        return success

def main():
    """主函数"""
    tester = TokenIntegrationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！Token自动设置功能集成成功！")
        return 0
    else:
        print("\n💥 部分测试失败，请检查问题！")
        return 1

if __name__ == "__main__":
    import sys
    result = main()
    sys.exit(result)
