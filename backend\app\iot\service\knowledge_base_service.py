#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库服务层

提供知识库管理的业务逻辑处理，调用外部知识库服务 API
"""
import json
import time
from datetime import datetime
from typing import List, Optional
from uuid import uuid4
import httpx

from backend.app.iot.schema.knowledge_base import (
    KnowledgeBaseCreate,
    KnowledgeBaseInfo,
    KnowledgeBaseQuery,
    KnowledgeBaseStats,
    KnowledgeBaseUpdate,
    ParserConfig
)
from backend.core.conf import settings
from backend.database.redis import redis_client


class KnowledgeBaseService:
    """知识库服务类"""

    def __init__(self):
        self.redis_prefix = "iot:kb:"
        self.stats_key = "iot:kb:stats"
        # 外部知识库服务配置
        self.kb_base_url = getattr(settings, 'KNOWLEDGE_BASE_URL', 'http://192.168.66.40:6610')
        self.kb_api_key = getattr(settings, 'KNOWLEDGE_BASE_API_KEY', 'ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW')
        self.timeout = getattr(settings, 'KNOWLEDGE_BASE_TIMEOUT', 30.0)

    async def _make_kb_request(self, method: str, endpoint: str, data: dict = None, params: dict = None) -> dict:
        """
        向外部知识库服务发起请求

        :param method: HTTP 方法
        :param endpoint: API 端点
        :param data: 请求数据
        :param params: 查询参数
        :return: 响应数据
        """
        url = f"{self.kb_base_url}{endpoint}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.kb_api_key}"
        }

        # 使用与测试脚本相同的httpx配置
        async with httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout, connect=10.0),
            verify=False,  # 禁用SSL验证
            headers={"User-Agent": "FastAPI-Best-Architecture/1.0"},
            follow_redirects=True
        ) as client:
            try:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=headers, json=data)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=headers, json=data)
                else:
                    raise ValueError(f"不支持的 HTTP 方法: {method}")

                response.raise_for_status()
                return response.json()

            except httpx.TimeoutException:
                raise Exception("知识库服务请求超时")
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 401:
                    raise Exception("知识库服务认证失败")
                elif e.response.status_code == 403:
                    raise Exception("知识库服务权限不足")
                elif e.response.status_code == 404:
                    raise Exception("知识库不存在")
                else:
                    raise Exception(f"知识库服务请求失败: {e.response.status_code}")
            except Exception as e:
                raise Exception(f"知识库服务连接失败: {str(e)}")

    async def health_check(self) -> dict:
        """
        检查知识库服务健康状态

        :return: 健康状态信息
        """
        try:
            # 尝试连接RAGFlow服务 - 获取数据集列表
            response = await self._make_kb_request("GET", "/api/v1/datasets", params={"page": 1, "page_size": 1})

            # 检查响应格式
            if isinstance(response, dict) and response.get("code") == 0:
                return {
                    "status": "healthy",
                    "service_url": self.kb_base_url,
                    "api_key_configured": bool(self.kb_api_key),
                    "response_code": response.get("code", -1),
                    "message": "RAGFlow服务连接正常",
                    "data_count": len(response.get("data", []))
                }
            else:
                return {
                    "status": "unhealthy",
                    "service_url": self.kb_base_url,
                    "api_key_configured": bool(self.kb_api_key),
                    "response_code": response.get("code", -1) if isinstance(response, dict) else -1,
                    "error": response.get("message", "未知错误") if isinstance(response, dict) else str(response),
                    "message": "RAGFlow服务响应异常"
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "service_url": self.kb_base_url,
                "api_key_configured": bool(self.kb_api_key),
                "error": str(e),
                "message": "RAGFlow服务连接失败"
            }

    def _convert_to_kb_info(self, raw_data: dict, user_id: str) -> KnowledgeBaseInfo:
        """
        将外部 API 响应转换为内部知识库信息格式

        :param raw_data: 外部 API 响应数据
        :param user_id: 用户ID
        :return: 知识库信息
        """
        try:
            kb_info = KnowledgeBaseInfo(
                id=raw_data.get("id", ""),
                name=raw_data.get("name", ""),
                avatar=raw_data.get("avatar"),
                description=raw_data.get("description"),
                embedding_model=raw_data.get("embedding_model", "unknown"),
                permission=raw_data.get("permission", "me"),
                chunk_method=raw_data.get("chunk_method", "naive"),
                pagerank=raw_data.get("pagerank", 0),
                parser_config=raw_data.get("parser_config", {}),
                chunk_count=raw_data.get("chunk_count", 0),
                document_count=raw_data.get("document_count", 0),
                token_num=raw_data.get("token_num", 0),
                status=raw_data.get("status", "1"),
                language=raw_data.get("language", "Chinese"),
                similarity_threshold=raw_data.get("similarity_threshold", 0.2),
                vector_similarity_weight=raw_data.get("vector_similarity_weight", 0.3),
                create_time=raw_data.get("create_time", int(time.time() * 1000)),
                update_time=raw_data.get("update_time", int(time.time() * 1000)),
                create_date=raw_data.get("create_date", datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")),
                update_date=raw_data.get("update_date", datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")),
                created_by=raw_data.get("created_by", user_id),
                tenant_id=raw_data.get("tenant_id", user_id)
            )

            return kb_info

        except Exception as e:
            raise Exception(f"知识库数据转换失败: {str(e)}")
    
    async def create_knowledge_base(self, kb_data: KnowledgeBaseCreate, user_id: str) -> KnowledgeBaseInfo:
        """
        创建知识库

        :param kb_data: 知识库创建数据
        :param user_id: 用户ID
        :return: 创建的知识库信息
        """
        # 准备外部 API 请求数据
        request_data = {
            "name": kb_data.name
        }

        # 添加可选字段
        if kb_data.avatar:
            request_data["avatar"] = kb_data.avatar
        if kb_data.description:
            request_data["description"] = kb_data.description
        if kb_data.embedding_model:
            request_data["embedding_model"] = kb_data.embedding_model
        if kb_data.permission:
            request_data["permission"] = kb_data.permission
        if kb_data.chunk_method:
            request_data["chunk_method"] = kb_data.chunk_method
        if kb_data.pagerank is not None:
            request_data["pagerank"] = kb_data.pagerank

        # 添加解析器配置
        if kb_data.parser_config:
            request_data["parser_config"] = kb_data.parser_config.model_dump(exclude_none=True)

        # 调用外部知识库服务创建知识库
        try:
            response = await self._make_kb_request("POST", "/api/v1/datasets", data=request_data)

            # 检查响应
            if response.get("code") == 0 and response.get("data"):
                kb_info = self._convert_to_kb_info(response["data"], user_id)

                # 缓存到 Redis（可选，用于快速访问）
                await self._cache_knowledge_base(kb_info, user_id)

                return kb_info
            else:
                error_msg = response.get("message", "创建知识库失败")
                if "already exists" in error_msg.lower() or "已存在" in error_msg:
                    raise ValueError(f"知识库名称 '{kb_data.name}' 已存在")
                else:
                    raise Exception(error_msg)

        except Exception as e:
            if "已存在" in str(e) or "already exists" in str(e).lower():
                raise ValueError(f"知识库名称 '{kb_data.name}' 已存在")
            else:
                raise Exception(f"创建知识库失败: {str(e)}")
    
    async def get_knowledge_base(self, kb_id: str, user_id: str) -> Optional[KnowledgeBaseInfo]:
        """
        获取知识库详情

        :param kb_id: 知识库ID
        :param user_id: 用户ID
        :return: 知识库信息
        """
        try:
            # 先尝试从缓存获取
            cached_data = await redis_client.get(f"{self.redis_prefix}{kb_id}")
            if cached_data:
                kb_info = KnowledgeBaseInfo.model_validate(json.loads(cached_data))
                if await self._check_permission(kb_info, user_id):
                    return kb_info

            # 从外部服务获取
            response = await self._make_kb_request("GET", f"/api/v1/datasets/{kb_id}")

            if response.get("code") == 0 and response.get("data"):
                kb_info = self._convert_to_kb_info(response["data"], user_id)

                # 检查权限
                if await self._check_permission(kb_info, user_id):
                    # 更新缓存
                    await self._cache_knowledge_base(kb_info, user_id)
                    return kb_info

            return None

        except Exception:
            return None
    
    async def update_knowledge_base(self, kb_id: str, kb_data: KnowledgeBaseUpdate, user_id: str) -> bool:
        """
        更新知识库

        :param kb_id: 知识库ID
        :param kb_data: 更新数据
        :param user_id: 用户ID
        :return: 是否成功
        """
        try:
            # 获取现有知识库
            kb_info = await self.get_knowledge_base(kb_id, user_id)
            if not kb_info:
                return False

            # 检查是否有权限修改
            if kb_info.created_by != user_id:
                return False

            # 准备更新数据
            update_data = kb_data.model_dump(exclude_unset=True)

            # 检查嵌入模型更新条件
            if update_data.get("embedding_model") and kb_info.chunk_count > 0:
                raise ValueError("chunk_count 不为 0 时无法更新嵌入模型")

            # 调用外部知识库服务更新
            response = await self._make_kb_request("PUT", f"/api/v1/datasets/{kb_id}", data=update_data)

            if response.get("code") == 0:
                # 清除缓存
                cache_key = f"{self.redis_prefix}{user_id}:{kb_id}"
                await redis_client.delete(cache_key)
                return True
            else:
                error_msg = response.get("message", "更新知识库失败")
                if "already exists" in error_msg.lower() or "已存在" in error_msg:
                    raise ValueError(f"知识库名称已存在")
                else:
                    raise Exception(error_msg)

        except Exception as e:
            if "已存在" in str(e) or "already exists" in str(e).lower():
                raise ValueError(f"知识库名称已存在")
            else:
                return False
    
    async def delete_knowledge_bases(self, kb_ids: Optional[List[str]], user_id: str) -> bool:
        """
        删除知识库

        :param kb_ids: 知识库ID列表，None表示删除所有
        :param user_id: 用户ID
        :return: 是否成功
        """
        try:
            if kb_ids is None:
                # 删除用户所有知识库
                user_kbs = await self.list_knowledge_bases(
                    KnowledgeBaseQuery(page=1, page_size=1000), user_id
                )
                kb_ids = [kb.id for kb in user_kbs]

            if not kb_ids:
                return True

            # 检查权限并删除
            valid_kb_ids = []
            for kb_id in kb_ids:
                kb_info = await self.get_knowledge_base(kb_id, user_id)
                if kb_info and kb_info.created_by == user_id:
                    valid_kb_ids.append(kb_id)

            if not valid_kb_ids:
                return True

            # 调用外部知识库服务删除
            delete_data = {"ids": valid_kb_ids}
            response = await self._make_kb_request("DELETE", "/api/v1/datasets", data=delete_data)

            if response.get("code") == 0:
                # 清除缓存
                for kb_id in valid_kb_ids:
                    cache_key = f"{self.redis_prefix}{user_id}:{kb_id}"
                    await redis_client.delete(cache_key)
                return True
            else:
                return False

        except Exception:
            return False
    
    async def list_knowledge_bases(self, query: KnowledgeBaseQuery, user_id: str) -> List[KnowledgeBaseInfo]:
        """
        获取知识库列表

        :param query: 查询参数
        :param user_id: 用户ID
        :return: 知识库列表
        """
        try:
            # 准备查询参数
            params = {
                "page": query.page,
                "page_size": query.page_size,
                "orderby": query.orderby,
                "desc": query.desc
            }

            # 添加过滤条件
            if query.name:
                params["name"] = query.name
            if query.id:
                params["id"] = query.id

            # 调用外部知识库服务
            response = await self._make_kb_request("GET", "/api/v1/datasets", params=params)

            if response.get("code") == 0 and response.get("data"):
                kb_list = []
                for raw_kb in response["data"]:
                    kb_info = self._convert_to_kb_info(raw_kb, user_id)

                    # 权限检查
                    if await self._check_permission(kb_info, user_id):
                        kb_list.append(kb_info)
                        # 缓存知识库信息
                        await self._cache_knowledge_base(kb_info, user_id)

                return kb_list
            else:
                return []

        except Exception as e:
            # 如果外部服务不可用，尝试从缓存获取
            return await self._get_cached_knowledge_bases(query, user_id)
    
    async def get_knowledge_base_stats(self, user_id: str) -> KnowledgeBaseStats:
        """
        获取知识库统计信息

        :param user_id: 用户ID
        :return: 统计信息
        """
        try:
            # 获取用户的所有知识库
            user_kbs = await self.list_knowledge_bases(
                KnowledgeBaseQuery(page=1, page_size=1000), user_id
            )

            total_kb = len(user_kbs)
            total_documents = sum(kb.document_count for kb in user_kbs)
            total_chunks = sum(kb.chunk_count for kb in user_kbs)
            total_tokens = sum(kb.token_num for kb in user_kbs)
            active_kb = sum(1 for kb in user_kbs if kb.status == "1")

            # 计算最近创建的知识库数（7天内）
            week_ago = int((time.time() - 7 * 24 * 3600) * 1000)
            recent_created = sum(1 for kb in user_kbs if kb.create_time > week_ago)

            # 估算存储使用量
            storage_used = f"{total_tokens * 4 / 1024 / 1024:.2f} MB"

            return KnowledgeBaseStats(
                total_kb=total_kb,
                total_documents=total_documents,
                total_chunks=total_chunks,
                total_tokens=total_tokens,
                active_kb=active_kb,
                recent_created=recent_created,
                storage_used=storage_used,
                last_update=datetime.now()
            )

        except Exception:
            # 返回默认统计信息
            return KnowledgeBaseStats(
                total_kb=0,
                total_documents=0,
                total_chunks=0,
                total_tokens=0,
                active_kb=0,
                recent_created=0,
                storage_used="0 MB",
                last_update=datetime.now()
            )
    
    async def _cache_knowledge_base(self, kb_info: KnowledgeBaseInfo, user_id: str) -> None:
        """缓存知识库到Redis"""
        cache_key = f"{self.redis_prefix}{user_id}:{kb_info.id}"
        await redis_client.set(
            cache_key,
            kb_info.model_dump_json(),
            ex=3600  # 1小时过期
        )

    async def _get_cached_knowledge_bases(self, query: KnowledgeBaseQuery, user_id: str) -> List[KnowledgeBaseInfo]:
        """从缓存获取知识库列表"""
        pattern = f"{self.redis_prefix}{user_id}:*"
        keys = await redis_client.keys(pattern)

        kb_list = []
        for key in keys:
            kb_data = await redis_client.get(key)
            if kb_data:
                try:
                    kb_info = KnowledgeBaseInfo.model_validate(json.loads(kb_data))

                    # 名称过滤
                    if query.name and query.name.lower() not in kb_info.name.lower():
                        continue

                    # ID过滤
                    if query.id and kb_info.id != query.id:
                        continue

                    kb_list.append(kb_info)
                except Exception:
                    continue

        # 排序
        if query.orderby == "create_time":
            kb_list.sort(key=lambda x: x.create_time, reverse=query.desc)
        elif query.orderby == "update_time":
            kb_list.sort(key=lambda x: x.update_time, reverse=query.desc)

        # 分页
        start = (query.page - 1) * query.page_size
        end = start + query.page_size

        return kb_list[start:end]
    
    async def _is_name_exists(self, name: str, user_id: str) -> bool:
        """检查名称是否已存在"""
        try:
            user_kbs = await self.list_knowledge_bases(
                KnowledgeBaseQuery(page=1, page_size=1000), user_id
            )
            return any(kb.name.lower() == name.lower() for kb in user_kbs)
        except Exception:
            return False
    
    async def _check_permission(self, kb_info: KnowledgeBaseInfo, user_id: str) -> bool:
        """检查用户是否有权限访问知识库"""
        # 临时简化权限检查 - 允许所有用户访问所有知识库
        # TODO: 实现正确的权限映射逻辑
        return True

        # 原始权限检查逻辑（暂时注释）
        # if kb_info.permission == "me":
        #     return kb_info.created_by == user_id
        # elif kb_info.permission == "team":
        #     return kb_info.tenant_id == user_id
        # return False
    
    async def _update_stats_on_create(self) -> None:
        """创建知识库时更新统计"""
        # 这里可以实现全局统计更新逻辑
        pass

    async def _update_stats_on_delete(self, count: int) -> None:
        """删除知识库时更新统计"""
        # 这里可以实现全局统计更新逻辑
        _ = count  # 避免未使用参数警告
        pass


# 创建服务实例
knowledge_base_service = KnowledgeBaseService()
