<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token自动设置功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin: 10px 0;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .token-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .log-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Token自动设置功能测试</h1>
        
        <!-- Token状态检查 -->
        <div class="test-section">
            <h3>1. Token状态检查</h3>
            <button onclick="checkTokenStatus()">检查Token状态</button>
            <button onclick="clearAllTokens()">清除所有Token</button>
            <div id="tokenStatus"></div>
            <div id="tokenDisplay"></div>
        </div>
        
        <!-- 模拟登录 -->
        <div class="test-section">
            <h3>2. 模拟登录设置Token</h3>
            <button onclick="simulateLogin()">模拟Java系统登录</button>
            <button onclick="simulateLogout()">模拟退出登录</button>
            <div id="loginStatus"></div>
        </div>
        
        <!-- 知识库API测试 -->
        <div class="test-section">
            <h3>3. 知识库API调用测试</h3>
            <button onclick="testHealthAPI()">测试健康检查接口</button>
            <button onclick="testKnowledgeBaseAPI()">测试知识库列表接口</button>
            <button onclick="testStatsAPI()">测试统计信息接口</button>
            <div id="apiTestResults"></div>
        </div>
        
        <!-- 前端页面集成测试 -->
        <div class="test-section">
            <h3>4. 前端页面集成测试</h3>
            <button onclick="openKnowledgeBasePage()">打开知识库管理页面</button>
            <button onclick="testPageIntegration()">测试页面Token集成</button>
            <div id="integrationResults"></div>
        </div>
        
        <!-- 日志输出 -->
        <div class="test-section">
            <h3>5. 测试日志</h3>
            <button onclick="clearLogs()">清除日志</button>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(logEntry);
        }

        function clearLogs() {
            document.getElementById('logArea').textContent = '';
        }

        // Token管理函数
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        function setCookie(name, value, days = 1) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${value}; expires=${expires.toUTCString()}; path=/`;
        }

        function getTokenFromStorage() {
            // 按优先级获取token
            return getCookie('token') || 
                   sessionStorage.getItem('token') || 
                   localStorage.getItem('token');
        }

        function setTokenToAllStorage(token) {
            setCookie('token', token);
            sessionStorage.setItem('token', token);
            localStorage.setItem('token', token);
        }

        function clearAllTokens() {
            // 清除所有存储位置的token
            document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            sessionStorage.removeItem('token');
            localStorage.removeItem('token');
            
            log('已清除所有存储位置的token');
            checkTokenStatus();
        }

        // Token状态检查
        function checkTokenStatus() {
            const cookieToken = getCookie('token');
            const sessionToken = sessionStorage.getItem('token');
            const localToken = localStorage.getItem('token');
            
            let statusHtml = '';
            let hasToken = false;
            
            if (cookieToken) {
                statusHtml += `<div class="status success">Cookie Token: 存在 (${cookieToken.substring(0, 20)}...)</div>`;
                hasToken = true;
            } else {
                statusHtml += `<div class="status error">Cookie Token: 不存在</div>`;
            }
            
            if (sessionToken) {
                statusHtml += `<div class="status success">SessionStorage Token: 存在 (${sessionToken.substring(0, 20)}...)</div>`;
                hasToken = true;
            } else {
                statusHtml += `<div class="status error">SessionStorage Token: 不存在</div>`;
            }
            
            if (localToken) {
                statusHtml += `<div class="status success">LocalStorage Token: 存在 (${localToken.substring(0, 20)}...)</div>`;
                hasToken = true;
            } else {
                statusHtml += `<div class="status error">LocalStorage Token: 不存在</div>`;
            }
            
            document.getElementById('tokenStatus').innerHTML = statusHtml;
            
            // 显示当前使用的token
            const currentToken = getTokenFromStorage();
            if (currentToken) {
                document.getElementById('tokenDisplay').innerHTML = 
                    `<div class="token-display">当前Token: ${currentToken}</div>`;
                log(`Token状态检查完成，当前token: ${currentToken.substring(0, 30)}...`);
            } else {
                document.getElementById('tokenDisplay').innerHTML = 
                    `<div class="status warning">未找到有效Token</div>`;
                log('Token状态检查完成，未找到有效token');
            }
            
            return hasToken;
        }

        // 模拟登录
        function simulateLogin() {
            // 模拟Java系统登录后的token
            const mockToken = 'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjQ1N2I1ZjJjLTY0ODItNDdjMy1iMjJjLTBjZjZhNWNmMzUwOCJ9.YEfuSy_rl2g4_9rbSd4A34qtatSWhOBfMe-Rzw0c3McGJQjMBIUs_9VWxEx8inrDU5asAtVTYTyl0m6bVmYfzw';
            
            setTokenToAllStorage(mockToken);
            
            document.getElementById('loginStatus').innerHTML = 
                `<div class="status success">模拟登录成功！Token已设置到所有存储位置</div>`;
            
            log('模拟登录成功，token已设置');
            checkTokenStatus();
        }

        function simulateLogout() {
            clearAllTokens();
            document.getElementById('loginStatus').innerHTML = 
                `<div class="status info">模拟退出登录成功！所有Token已清除</div>`;
            log('模拟退出登录成功');
        }

        // API测试函数
        async function makeAPIRequest(url, options = {}) {
            const token = getTokenFromStorage();
            
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` })
                }
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            try {
                log(`发起API请求: ${url}`);
                const response = await fetch(url, finalOptions);
                const data = await response.json();
                
                log(`API响应: ${response.status} - ${JSON.stringify(data).substring(0, 100)}...`);
                return { success: response.ok, status: response.status, data };
            } catch (error) {
                log(`API请求失败: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }

        async function testHealthAPI() {
            const result = await makeAPIRequest('/fastapi/api/v1/iot/kb/health');
            
            let resultHtml = '';
            if (result.success) {
                resultHtml = `<div class="status success">健康检查成功: ${result.data.msg || '服务正常'}</div>`;
            } else {
                resultHtml = `<div class="status error">健康检查失败: ${result.error || result.data?.msg || '未知错误'}</div>`;
            }
            
            document.getElementById('apiTestResults').innerHTML = resultHtml;
        }

        async function testKnowledgeBaseAPI() {
            const result = await makeAPIRequest('/fastapi/api/v1/iot/kb/datasets');
            
            let resultHtml = '';
            if (result.success) {
                const count = Array.isArray(result.data.data) ? result.data.data.length : 0;
                resultHtml = `<div class="status success">知识库列表获取成功: 共${count}个知识库</div>`;
            } else {
                if (result.status === 401) {
                    resultHtml = `<div class="status error">认证失败: 请检查token是否有效</div>`;
                } else {
                    resultHtml = `<div class="status error">知识库列表获取失败: ${result.error || result.data?.msg || '未知错误'}</div>`;
                }
            }
            
            document.getElementById('apiTestResults').innerHTML += resultHtml;
        }

        async function testStatsAPI() {
            const result = await makeAPIRequest('/fastapi/api/v1/iot/kb/datasets/stats/summary');
            
            let resultHtml = '';
            if (result.success) {
                resultHtml = `<div class="status success">统计信息获取成功: ${JSON.stringify(result.data.data).substring(0, 50)}...</div>`;
            } else {
                if (result.status === 401) {
                    resultHtml = `<div class="status error">认证失败: 请检查token是否有效</div>`;
                } else {
                    resultHtml = `<div class="status error">统计信息获取失败: ${result.error || result.data?.msg || '未知错误'}</div>`;
                }
            }
            
            document.getElementById('apiTestResults').innerHTML += resultHtml;
        }

        // 页面集成测试
        function openKnowledgeBasePage() {
            const url = window.location.origin + '/#/ai/kb/kbm';
            window.open(url, '_blank');
            log('已打开知识库管理页面');
        }

        function testPageIntegration() {
            const hasToken = checkTokenStatus();
            
            let resultHtml = '';
            if (hasToken) {
                resultHtml = `
                    <div class="status success">Token状态正常，页面应该能正常工作</div>
                    <div class="status info">请在知识库管理页面检查右上角的连接状态指示器</div>
                `;
            } else {
                resultHtml = `
                    <div class="status warning">未找到Token，请先模拟登录</div>
                    <div class="status info">知识库页面将显示"未连接"状态</div>
                `;
            }
            
            document.getElementById('integrationResults').innerHTML = resultHtml;
        }

        // 页面加载时自动检查token状态
        window.onload = function() {
            log('页面加载完成，开始检查token状态');
            checkTokenStatus();
        };
    </script>
</body>
</html>
