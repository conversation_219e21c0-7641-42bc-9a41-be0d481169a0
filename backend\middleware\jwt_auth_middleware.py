#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Any

from fastapi import Request, Response
from fastapi.security.utils import get_authorization_scheme_param
from starlette.authentication import AuthCredentials, AuthenticationBackend, AuthenticationError
from starlette.requests import HTTPConnection

from backend.app.admin.schema.user import GetUserInfoWithRelationDetail
from backend.common.exception.errors import TokenError
from backend.common.log import log
from backend.common.security.jwt import jwt_authentication
from backend.core.conf import settings
from backend.utils.serializers import MsgSpecJSONResponse


class _AuthenticationError(AuthenticationError):
    """重写内部认证错误类"""

    def __init__(
        self, *, code: int | None = None, msg: str | None = None, headers: dict[str, Any] | None = None
    ) -> None:
        """
        初始化认证错误

        :param code: 错误码
        :param msg: 错误信息
        :param headers: 响应头
        :return:
        """
        self.code = code
        self.msg = msg
        self.headers = headers


class JwtAuthMiddleware(AuthenticationBackend):
    """JWT 认证中间件"""

    @staticmethod
    def auth_exception_handler(conn: HTTPConnection, exc: _AuthenticationError) -> Response:
        """
        覆盖内部认证错误处理

        :param conn: HTTP 连接对象
        :param exc: 认证错误对象
        :return:
        """
        return MsgSpecJSONResponse(content={'code': exc.code, 'msg': exc.msg, 'data': None}, status_code=exc.code)

    async def authenticate(self, request: Request) -> tuple[AuthCredentials, GetUserInfoWithRelationDetail] | None:
        """
        认证请求

        :param request: FastAPI 请求对象
        :return:
        """
        authorization = request.headers.get('Authorization')
        path = request.url.path

        log.info(f"JWT认证中间件: 路径 {path} Authorization头: {authorization}")

        if not authorization:
            # 对于需要认证的路径，记录调试信息
            if path.startswith('/api/v1/iot/kb/') and not path.endswith('/health'):
                log.debug(f"JWT认证中间件: 路径 {path} 缺少Authorization头")
            return None

        if path in settings.TOKEN_REQUEST_PATH_EXCLUDE:
            log.debug(f"JWT认证中间件: 路径 {path} 在白名单中，跳过认证")
            return None
        for pattern in settings.TOKEN_REQUEST_PATH_EXCLUDE_PATTERN:
            if pattern.match(path):
                log.debug(f"JWT认证中间件: 路径 {path} 匹配白名单模式，跳过认证")
                return None

        scheme, token = get_authorization_scheme_param(authorization)
        log.info(f"JWT认证中间件: 路径 {path} scheme={scheme}, token={token[:20] if token else 'None'}...")

        if scheme.lower() != 'bearer':
            log.debug(f"JWT认证中间件: 路径 {path} Authorization头格式错误，scheme: {scheme}")
            return None

        try:
            log.debug(f"JWT认证中间件: 开始验证token: {token[:20]}...")

            # 优先尝试Java token验证
            user = await self._authenticate_java_token(token)
            if user:
                log.info(f"JWT认证中间件: 路径 {path} Java token认证成功，用户: {user.username}")
            else:
                log.debug(f"JWT认证中间件: Java token验证失败，尝试FastAPI token验证")
                # 回退到FastAPI token验证
                user = await jwt_authentication(token)
                log.debug(f"JWT认证中间件: 路径 {path} FastAPI token认证成功，用户: {user.username}")
        except TokenError as exc:
            log.warning(f"JWT认证中间件: 路径 {path} Token错误: {exc.detail}")
            raise _AuthenticationError(code=exc.code, msg=exc.detail, headers=exc.headers)
        except Exception as e:
            log.exception(f'JWT认证中间件: 路径 {path} 授权异常：{e}')
            raise _AuthenticationError(code=getattr(e, 'code', 500), msg=getattr(e, 'msg', 'Internal Server Error'))

        # 请注意，此返回使用非标准模式，所以在认证通过时，将丢失某些标准特性
        # 标准返回模式请查看：https://www.starlette.io/authentication/
        return AuthCredentials(['authenticated']), user

    async def _authenticate_java_token(self, token: str) -> GetUserInfoWithRelationDetail | None:
        """
        验证Java系统token

        :param token: 原始token字符串
        :return: FastAPI用户对象或None
        """
        try:
            from backend.common.security.java_user_adapter import JavaUserAdapter
            from backend.database.redis import redis_client
            import json

            log.info(f"Java token验证开始: token={token}")

            # 检查token格式 - Java token通常是UUID格式，不包含'.'
            if '.' in token:
                log.debug(f"Java token验证: token包含'.'，可能是JWT token，跳过Java验证")
                return None

            # 从Redis获取LoginUser对象
            redis_key = f"login_tokens:{token}"
            log.info(f"Java token验证: 查询Redis键: {redis_key}")

            login_user_data = await redis_client.get(redis_key)

            if not login_user_data:
                log.warning(f"Java token验证: Redis中未找到token {token}")
                return None

            log.info(f"Java token验证: 从Redis获取到数据，长度: {len(login_user_data)}")

            # 解析LoginUser对象
            try:
                login_user = json.loads(login_user_data)
                log.info(f"Java token验证: LoginUser解析成功，用户: {login_user.get('user', {}).get('userName', 'unknown')}")
            except json.JSONDecodeError as e:
                log.error(f"Java token验证: 解析LoginUser JSON失败: {str(e)}")
                return None

            # 验证LoginUser有效性
            if not JavaUserAdapter.validate_login_user(login_user):
                log.warning(f"Java token验证: LoginUser对象无效")
                return None

            # 转换为FastAPI用户对象
            fastapi_user = JavaUserAdapter.build_fastapi_user(login_user)

            log.info(f"Java token验证成功: {fastapi_user.username} (ID: {fastapi_user.id})")
            return fastapi_user

        except Exception as e:
            log.error(f"Java token验证异常: {str(e)}")
            import traceback
            log.error(f"异常堆栈: {traceback.format_exc()}")
            return None
