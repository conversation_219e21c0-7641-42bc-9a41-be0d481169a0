#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IoT 系统认证适配器

此模块提供 IoT 系统与 FastAPI Best Architecture 认证系统的适配功能
支持验证来自 TS-IOT-SYS 的 JWT token，并将其映射到现有的用户模型
"""
import json
from datetime import datetime
from typing import Optional

from jose import ExpiredSignatureError, JWTError, jwt
from sqlalchemy.ext.asyncio import AsyncSession

from backend.app.admin.model import User
from backend.app.admin.schema.user import GetUserInfoWithRelationDetail
from backend.common.exception import errors
from backend.core.conf import settings
from backend.database.db import async_db_session
from backend.database.redis import redis_client
from backend.utils.serializers import select_as_dict
from backend.utils.timezone import timezone


class IoTTokenPayload:
    """IoT Token 载荷数据类"""
    
    def __init__(self, user_id: int, username: str, session_id: str, expire_time: datetime):
        self.user_id = user_id
        self.username = username
        self.session_id = session_id
        self.expire_time = expire_time


class IoTAuthAdapter:
    """IoT 认证适配器"""
    
    def __init__(self):
        self.enabled = settings.IOT_INTEGRATION_ENABLED
        self.secret_key = settings.IOT_JWT_SECRET_KEY
        self.algorithm = settings.IOT_JWT_ALGORITHM
        self.issuer = settings.IOT_JWT_ISSUER
        
    def is_enabled(self) -> bool:
        """检查 IoT 集成是否启用"""
        return self.enabled and bool(self.secret_key)
    
    def validate_config(self) -> bool:
        """验证 IoT 配置是否有效"""
        if not self.enabled:
            return False
        if not self.secret_key or self.secret_key == '':
            return False
        return True
    
    def decode_iot_jwt(self, token: str) -> IoTTokenPayload:
        """
        解析 IoT 系统的 JWT token

        :param token: IoT JWT token
        :return: IoT Token 载荷
        :raises: TokenError 当 token 无效或过期时
        """
        try:
            # 移除Bearer前缀
            if token.startswith('Bearer '):
                token = token[7:]

            # 解码JWT（不验证exp，因为Java后端的token可能没有exp字段）
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
                options={'verify_exp': False}  # 不验证exp，因为Java token可能没有这个字段
            )

            # 提取 IoT token 中的关键信息（基于Java后端的token结构）
            login_user_key = payload.get('login_user_key')  # Java后端使用的UUID
            user_id = payload.get('userid') or payload.get('user_id') or payload.get('sub')
            username = payload.get('username') or payload.get('preferred_username')
            session_id = login_user_key or payload.get('jti') or payload.get('session_id')
            expire = payload.get('exp')

            # 对于Java后端，login_user_key是必需的
            if not login_user_key:
                raise errors.TokenError(msg='IoT Token 格式无效：缺少login_user_key')

            # 如果没有user_id，使用login_user_key作为临时标识
            if not user_id:
                user_id = login_user_key

            # 如果没有username，使用默认值
            if not username:
                username = f"user_{login_user_key[:8]}"

            # 转换用户ID为整数（如果可能）
            if isinstance(user_id, str) and user_id.isdigit():
                user_id = int(user_id)

            # 如果JWT中没有exp字段，使用一个默认的过期时间（从Redis中获取实际过期时间）
            if expire:
                expire_time = timezone.from_datetime(timezone.to_utc(expire))
            else:
                # 使用当前时间+24小时作为默认过期时间，实际过期时间从Redis验证
                from datetime import datetime, timedelta
                expire_time = timezone.from_datetime(datetime.utcnow() + timedelta(hours=24))

            return IoTTokenPayload(
                user_id=user_id,
                username=username,
                session_id=session_id or str(user_id),
                expire_time=expire_time
            )

        except ExpiredSignatureError:
            raise errors.TokenError(msg='IoT Token 已过期')
        except (JWTError, ValueError, Exception) as e:
            if settings.IOT_DEBUG_MODE:
                raise errors.TokenError(msg=f'IoT Token 无效: {str(e)}')
            raise errors.TokenError(msg='IoT Token 无效')
    
    async def verify_iot_token_in_redis(self, token_payload: IoTTokenPayload) -> tuple[bool, dict]:
        """
        在 Redis 中验证 IoT token 是否有效
        支持多种Redis key格式

        :param token_payload: IoT Token 载荷
        :return: (token 是否有效, LoginUser数据字典)
        """
        try:
            session_id = token_payload.session_id

            # 尝试多种可能的Redis key格式
            possible_keys = [
                f"login_tokens:{session_id}",  # 标准格式
                f"login_tokens",              # 简化格式
                session_id,                   # 直接使用session_id
                f"token:{session_id}",        # 备选格式
            ]

            if settings.IOT_DEBUG_MODE:
                print(f"查找session_id: {session_id}")
                print(f"尝试的Redis keys: {possible_keys}")

            # 逐个尝试可能的key
            for redis_key in possible_keys:
                cached_data = await redis_client.get(redis_key)

                if cached_data:
                    if settings.IOT_DEBUG_MODE:
                        print(f"✅ 在key '{redis_key}' 找到数据，长度: {len(cached_data)}")

                    # 优先尝试解析JSON数据
                    try:
                        import json
                        login_user_data = json.loads(cached_data)
                        stored_token = login_user_data.get('token', '')

                        if stored_token == session_id:
                            # 检查过期时间
                            expire_time = login_user_data.get('expireTime', 0)
                            if expire_time > 0:
                                import time
                                current_time_ms = int(time.time() * 1000)
                                if current_time_ms > expire_time:
                                    if settings.IOT_DEBUG_MODE:
                                        print(f"❌ Token已过期: {expire_time} < {current_time_ms}")
                                    continue  # 继续尝试其他key

                            if settings.IOT_DEBUG_MODE:
                                print(f"✅ 找到有效的LoginUser数据: {stored_token}")
                            return True, login_user_data

                    except json.JSONDecodeError:
                        # 如果不是JSON，检查数据中是否包含我们的token
                        if session_id in cached_data:
                            if settings.IOT_DEBUG_MODE:
                                print(f"✅ 非JSON数据中包含session_id: {session_id}")
                            return True, {}

                        if settings.IOT_DEBUG_MODE:
                            print(f"⚠️ 无法解析JSON数据: {redis_key}")
                        continue

            # 如果所有key都没找到，搜索包含session_id的所有keys
            if settings.IOT_DEBUG_MODE:
                print(f"❌ 未找到匹配的数据，搜索包含session_id的keys...")
                matching_keys = []
                async for key in redis_client.scan_iter(match=f"*{session_id}*"):
                    matching_keys.append(key)

                if matching_keys:
                    print(f"找到包含session_id的keys: {matching_keys}")
                else:
                    print(f"未找到任何包含session_id的keys")

            # Redis验证失败
            if settings.IOT_DEBUG_MODE:
                print(f"❌ Redis验证失败，token无效或已过期")
            return False, {}

        except Exception as e:
            if settings.IOT_DEBUG_MODE:
                print(f"❌ Redis验证异常: {e}")
            return False, {}

    async def create_user_from_login_data(self, login_user_data: dict, token_payload: IoTTokenPayload) -> GetUserInfoWithRelationDetail:
        """
        从Redis中的LoginUser数据直接构造用户信息

        :param login_user_data: Redis中的LoginUser数据
        :param token_payload: JWT token载荷
        :return: 用户信息详情
        """
        try:
            # 从LoginUser数据中提取用户信息
            user_info = login_user_data.get('user', {})
            user_id = login_user_data.get('userId', user_info.get('userId', 1))
            username = login_user_data.get('username', user_info.get('userName', 'admin'))
            nickname = user_info.get('nickName', username)
            email = user_info.get('email', '<EMAIL>')  # 使用实际邮箱
            phone = user_info.get('phonenumber', '15888888888')  # 使用实际手机号
            dept_id = login_user_data.get('deptId', user_info.get('deptId', 1))

            # 生成UUID
            import uuid
            user_uuid = str(uuid.uuid4())

            # 构造用户数据字典
            user_data = {
                'id': int(user_id) if str(user_id).isdigit() else 1,
                'uuid': user_uuid,
                'username': username,
                'nickname': nickname,
                'email': email,
                'phone': phone,
                'avatar': None,  # 设置为None而不是空字符串
                'status': 1,  # 激活状态
                'is_superuser': True,  # IoT系统用户默认为超级用户
                'is_staff': True,
                'is_multi_login': True,
                'join_time': timezone.now(),
                'last_login_time': timezone.now(),
                'dept_id': dept_id,
                'roles': [],  # 简化处理，不设置角色
                'dept': None,  # 简化处理，不设置部门
                'permissions': ['*:*:*']  # 给予所有权限
            }

            if settings.IOT_DEBUG_MODE:
                print(f"✅ 从LoginUser数据构造用户: {username} (ID: {user_id})")

            return GetUserInfoWithRelationDetail(**user_data)

        except Exception as e:
            if settings.IOT_DEBUG_MODE:
                print(f"❌ 从LoginUser数据构造用户失败: {e}")
            raise errors.TokenError(msg=f'构造用户信息失败: {str(e)}')

    async def get_or_create_user(self, db: AsyncSession, token_payload: IoTTokenPayload) -> User:
        """
        根据 IoT token 信息获取或创建用户
        
        :param db: 数据库会话
        :param token_payload: IoT Token 载荷
        :return: 用户对象
        """
        from backend.app.admin.crud.crud_user import user_dao
        
        # 首先尝试通过用户名查找现有用户
        user = await user_dao.get_by_username(db, token_payload.username)
        
        if user:
            # 用户已存在，检查状态
            if not user.status:
                raise errors.AuthorizationError(msg='用户已被锁定，请联系系统管理员')
            return user
        
        # 用户不存在，根据配置决定是否自动创建
        # 这里可以根据实际需求实现自动创建逻辑
        # 目前先抛出异常，要求用户必须在系统中预先存在
        raise errors.TokenError(msg=f'用户 {token_payload.username} 不存在，请联系系统管理员')
    
    async def get_iot_user_permissions(self, user: User) -> list[str]:
        """
        获取 IoT 用户的权限列表
        
        :param user: 用户对象
        :return: 权限列表
        """
        permissions = []
        
        # 超级管理员拥有所有权限
        if user.is_superuser:
            return ['*:*:*']
        
        # 从用户角色中获取权限
        for role in user.roles:
            if role.status == 1:  # 角色状态正常
                for menu in role.menus:
                    if menu.status == 1 and menu.perms:  # 菜单状态正常且有权限标识
                        permissions.extend(menu.perms.split(','))
        
        # 去重并返回
        return list(set(permissions))
    
    async def cache_iot_user_info(self, user: User, permissions: list[str]) -> None:
        """
        缓存 IoT 用户信息到 Redis
        
        :param user: 用户对象
        :param permissions: 权限列表
        """
        try:
            # 缓存用户基本信息
            user_key = f"{settings.IOT_USER_PREFIX}{user.id}"
            user_info = {
                'id': user.id,
                'username': user.username,
                'nickname': user.nickname,
                'email': user.email,
                'is_superuser': user.is_superuser,
                'is_staff': user.is_staff,
                'status': user.status,
                'dept_id': user.dept_id
            }
            await redis_client.setex(
                user_key,
                settings.IOT_USER_CACHE_TTL,
                json.dumps(user_info, ensure_ascii=False)
            )
            
            # 缓存用户权限
            permission_key = f"{settings.IOT_PERMISSION_PREFIX}{user.id}"
            await redis_client.setex(
                permission_key,
                settings.IOT_PERMISSION_CACHE_TTL,
                json.dumps(permissions, ensure_ascii=False)
            )
            
        except Exception as e:
            if settings.IOT_DEBUG_MODE:
                print(f"缓存用户信息失败: {e}")
    
    async def get_cached_iot_user_info(self, user_id: int) -> Optional[dict]:
        """
        从 Redis 获取缓存的 IoT 用户信息
        
        :param user_id: 用户ID
        :return: 用户信息字典或 None
        """
        try:
            user_key = f"{settings.IOT_USER_PREFIX}{user_id}"
            cached_data = await redis_client.get(user_key)
            
            if cached_data:
                return json.loads(cached_data)
            return None
            
        except Exception as e:
            if settings.IOT_DEBUG_MODE:
                print(f"获取缓存用户信息失败: {e}")
            return None
    
    async def get_cached_iot_permissions(self, user_id: int) -> Optional[list[str]]:
        """
        从 Redis 获取缓存的 IoT 用户权限
        
        :param user_id: 用户ID
        :return: 权限列表或 None
        """
        try:
            permission_key = f"{settings.IOT_PERMISSION_PREFIX}{user_id}"
            cached_data = await redis_client.get(permission_key)
            
            if cached_data:
                return json.loads(cached_data)
            return None
            
        except Exception as e:
            if settings.IOT_DEBUG_MODE:
                print(f"获取缓存权限信息失败: {e}")
            return None
    
    async def authenticate_iot_token(self, token: str) -> GetUserInfoWithRelationDetail:
        """
        IoT Token 认证主流程
        
        :param token: IoT JWT token
        :return: 用户信息详情
        """
        if not self.is_enabled():
            raise errors.TokenError(msg='IoT 集成未启用')
        
        # 1. 解析 JWT token
        token_payload = self.decode_iot_jwt(token)
        
        # 2. 验证 token 在 Redis 中的有效性并获取LoginUser数据
        is_valid, login_user_data = await self.verify_iot_token_in_redis(token_payload)
        if not is_valid:
            raise errors.TokenError(msg='IoT Token 已失效')
        
        # 3. 如果有LoginUser数据，直接从中构造用户信息
        if login_user_data:
            return await self.create_user_from_login_data(login_user_data, token_payload)

        # 4. 尝试从缓存获取用户信息
        cached_user = await self.get_cached_iot_user_info(token_payload.user_id)
        cached_permissions = await self.get_cached_iot_permissions(token_payload.user_id)

        if cached_user and cached_permissions:
            # 从缓存构造用户对象
            user_data = cached_user.copy()
            user_data['roles'] = []  # 简化处理
            user_data['dept'] = None
            user_data['permissions'] = cached_permissions

            return GetUserInfoWithRelationDetail(**user_data)

        # 5. 从数据库获取用户信息
        async with async_db_session() as db:
            user = await self.get_or_create_user(db, token_payload)
            permissions = await self.get_iot_user_permissions(user)

            # 6. 缓存用户信息
            await self.cache_iot_user_info(user, permissions)

            # 7. 构造返回对象
            user_dict = select_as_dict(user)
            user_dict['permissions'] = permissions

            # 确保包含必需的字段
            if 'roles' not in user_dict or user_dict['roles'] is None:
                user_dict['roles'] = []
            if 'dept' not in user_dict or user_dict['dept'] is None:
                user_dict['dept'] = None

            return GetUserInfoWithRelationDetail(**user_dict)


# 创建全局适配器实例
iot_adapter = IoTAuthAdapter()
