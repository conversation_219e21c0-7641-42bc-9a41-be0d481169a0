#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IoT 权限验证扩展

此模块扩展现有的 RBAC 权限验证系统，支持 IoT 系统的权限标识和验证逻辑
"""
from functools import wraps
from typing import List, Union

from fastapi import Depends, HTTPException, Request, status

from backend.common.exception import errors
from backend.common.security.iot_adapter import iot_adapter
from backend.common.security.jwt import DependsJwtAuth
from backend.core.conf import settings


class IoTRequestPermission:
    """
    IoT 请求权限验证器
    
    用于 IoT 系统的权限控制，支持 IoT 权限标识格式
    """
    
    def __init__(self, value: str) -> None:
        """
        初始化 IoT 权限验证器
        
        :param value: IoT 权限标识（如 'iot:device:list'）
        """
        self.value = value
    
    async def __call__(self, request: Request) -> None:
        """
        验证 IoT 请求权限
        
        :param request: FastAPI 请求对象
        """
        if not isinstance(self.value, str):
            raise errors.ServerError(msg='权限标识必须为字符串')
        
        # 将 IoT 权限标识附加到请求状态
        request.state.iot_permission = self.value


def require_iot_permission(permission: Union[str, List[str]], require_all: bool = False):
    """
    IoT 权限验证装饰器
    
    :param permission: 权限标识或权限列表
    :param require_all: 是否需要所有权限（仅当 permission 为列表时有效）
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取 request 对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="无法获取请求对象"
                )
            
            # 检查用户是否已认证
            if not hasattr(request, 'user') or not request.user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="用户未认证"
                )
            
            # 检查是否为 IoT 用户
            user_permissions = getattr(request.user, 'permissions', [])
            
            # 权限检查
            if isinstance(permission, str):
                if not has_iot_permission(user_permissions, permission):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"缺少 IoT 权限: {permission}"
                    )
            elif isinstance(permission, list):
                if require_all:
                    if not has_all_iot_permissions(user_permissions, permission):
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail=f"缺少 IoT 权限: {', '.join(permission)}"
                        )
                else:
                    if not has_any_iot_permission(user_permissions, permission):
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail=f"缺少任一 IoT 权限: {', '.join(permission)}"
                        )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def has_iot_permission(user_permissions: List[str], permission: str) -> bool:
    """
    检查用户是否具有指定的 IoT 权限
    
    :param user_permissions: 用户权限列表
    :param permission: 权限标识
    :return: 是否具有权限
    """
    # 超级管理员权限
    if '*:*:*' in user_permissions:
        return True
    
    # 精确匹配
    if permission in user_permissions:
        return True
    
    # 通配符匹配（如 'iot:device:*' 匹配 'iot:device:list'）
    permission_parts = permission.split(':')
    for user_perm in user_permissions:
        user_perm_parts = user_perm.split(':')
        
        if len(user_perm_parts) == len(permission_parts):
            match = True
            for i, (user_part, perm_part) in enumerate(zip(user_perm_parts, permission_parts)):
                if user_part != '*' and user_part != perm_part:
                    match = False
                    break
            if match:
                return True
    
    return False


def has_any_iot_permission(user_permissions: List[str], permissions: List[str]) -> bool:
    """
    检查用户是否具有任一 IoT 权限
    
    :param user_permissions: 用户权限列表
    :param permissions: 权限标识列表
    :return: 是否具有任一权限
    """
    return any(has_iot_permission(user_permissions, perm) for perm in permissions)


def has_all_iot_permissions(user_permissions: List[str], permissions: List[str]) -> bool:
    """
    检查用户是否具有所有 IoT 权限
    
    :param user_permissions: 用户权限列表
    :param permissions: 权限标识列表
    :return: 是否具有所有权限
    """
    return all(has_iot_permission(user_permissions, perm) for perm in permissions)


async def iot_rbac_verify(request: Request, _token: str = DependsJwtAuth) -> None:
    """
    IoT RBAC 权限校验
    
    :param request: FastAPI 请求对象
    :param _token: JWT 令牌
    """
    path = request.url.path
    
    # 检查是否为 IoT API 路径
    if not path.startswith('/api/v1/iot'):
        return
    
    # IoT 集成未启用时跳过验证
    if not iot_adapter.is_enabled():
        return
    
    # JWT 授权状态强制校验
    if not request.auth.scopes:
        raise errors.TokenError
    
    # 超级管理员免校验
    if hasattr(request.user, 'is_superuser') and request.user.is_superuser:
        return
    
    # 检查 IoT 权限标识
    iot_permission = getattr(request.state, 'iot_permission', None)
    
    if not iot_permission:
        # 没有权限标识不校验
        return
    
    # 获取用户权限
    user_permissions = getattr(request.user, 'permissions', [])

    # 验证权限
    if not has_iot_permission(user_permissions, iot_permission):
        from backend.common.log import log
        log.warning(f"IoT权限验证失败: 用户 {request.user.username} 缺少权限 {iot_permission}")
        raise errors.AuthorizationError(msg=f'缺少 IoT 权限: {iot_permission}')


def check_iot_permission(request: Request, permission: str) -> bool:
    """
    检查当前用户是否具有指定的 IoT 权限
    
    :param request: FastAPI 请求对象
    :param permission: 权限标识
    :return: 是否具有权限
    """
    if not hasattr(request, 'user') or not request.user:
        return False
    
    user_permissions = getattr(request.user, 'permissions', [])
    return has_iot_permission(user_permissions, permission)


def require_iot_superuser(request: Request) -> bool:
    """
    检查当前用户是否为 IoT 超级管理员
    
    :param request: FastAPI 请求对象
    :return: 是否为超级管理员
    """
    if not hasattr(request, 'user') or not request.user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户未认证"
        )
    
    user_permissions = getattr(request.user, 'permissions', [])
    if '*:*:*' not in user_permissions:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要 IoT 超级管理员权限"
        )
    
    return True


# IoT RBAC 授权依赖注入
DependsIoTRBAC = Depends(iot_rbac_verify)


# 常用的 IoT 权限常量
class IoTPermissions:
    """IoT 权限常量"""

    # 设备管理权限
    DEVICE_LIST = 'iot:device:list'
    DEVICE_VIEW = 'iot:device:view'
    DEVICE_ADD = 'iot:device:add'
    DEVICE_EDIT = 'iot:device:edit'
    DEVICE_DELETE = 'iot:device:delete'
    DEVICE_CONTROL = 'iot:device:control'

    # 数据管理权限
    DATA_VIEW = 'iot:data:view'
    DATA_EXPORT = 'iot:data:export'
    DATA_ANALYSIS = 'iot:data:analysis'

    # 用户管理权限
    USER_LIST = 'iot:user:list'
    USER_VIEW = 'iot:user:view'
    USER_ADD = 'iot:user:add'
    USER_EDIT = 'iot:user:edit'
    USER_DELETE = 'iot:user:delete'

    # 知识库管理权限
    KB_LIST = 'iot:kb:list'
    KB_VIEW = 'iot:kb:view'
    KB_CREATE = 'iot:kb:create'
    KB_EDIT = 'iot:kb:edit'
    KB_DELETE = 'iot:kb:delete'
    KB_MANAGE = 'iot:kb:manage'
    KB_STATS = 'iot:kb:stats'

    # 系统管理权限
    SYSTEM_CONFIG = 'iot:system:config'
    SYSTEM_MONITOR = 'iot:system:monitor'
    SYSTEM_LOG = 'iot:system:log'
