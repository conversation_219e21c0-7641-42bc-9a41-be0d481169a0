<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置JWT Token</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .token-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .btn {
            background: #409eff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #337ecc;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #f0f9ff;
            color: #067f23;
            border: 1px solid #b3d8ff;
        }
        .error {
            background: #fef0f0;
            color: #f56c6c;
            border: 1px solid #fbc4c4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JWT Token 设置工具</h1>
        
        <div>
            <label for="tokenInput">输入JWT Token:</label>
            <textarea id="tokenInput" class="token-input" rows="4" placeholder="请输入JWT token..."></textarea>
        </div>
        
        <div>
            <button class="btn" onclick="setToken()">设置Token</button>
            <button class="btn" onclick="clearToken()">清除Token</button>
            <button class="btn" onclick="testToken()">测试Token</button>
            <button class="btn" onclick="useValidToken()">使用有效Token</button>
        </div>
        
        <div id="status"></div>
        
        <div style="margin-top: 20px;">
            <h3>当前Token状态:</h3>
            <div id="currentToken" style="font-family: monospace; font-size: 12px; background: #f8f8f8; padding: 10px; border-radius: 4px;"></div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>使用说明:</h3>
            <ol>
                <li>点击"使用有效Token"按钮设置一个测试用的有效token</li>
                <li>或者手动输入您的JWT token并点击"设置Token"</li>
                <li>点击"测试Token"验证token是否有效</li>
                <li>设置成功后，刷新知识库管理页面即可正常使用</li>
            </ol>
        </div>
    </div>

    <script>
        // 有效的测试token
        const validToken = "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjQ1N2I1ZjJjLTY0ODItNDdjMy1iMjJjLTBjZjZhNWNmMzUwOCJ9.YEfuSy_rl2g4_9rbSd4A34qtatSWhOBfMe-Rzw0c3McGJQjMBIUs_9VWxEx8inrDU5asAtVTYTyl0m6bVmYfzw";
        
        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
        }
        
        function updateCurrentToken() {
            const token = getToken();
            const currentTokenDiv = document.getElementById('currentToken');
            if (token) {
                currentTokenDiv.textContent = `Cookie: ${document.cookie.includes('token') ? '已设置' : '未设置'}\nSessionStorage: ${sessionStorage.getItem('token') ? '已设置' : '未设置'}\nLocalStorage: ${localStorage.getItem('token') ? '已设置' : '未设置'}\n\nToken: ${token.substring(0, 50)}...`;
            } else {
                currentTokenDiv.textContent = '未设置token';
            }
        }
        
        function getToken() {
            // 按优先级获取token
            const cookieToken = document.cookie.split('; ').find(row => row.startsWith('token='))?.split('=')[1];
            return cookieToken || sessionStorage.getItem('token') || localStorage.getItem('token');
        }
        
        function setToken() {
            const token = document.getElementById('tokenInput').value.trim();
            if (!token) {
                showStatus('请输入token', true);
                return;
            }
            
            try {
                // 设置到多个存储位置
                document.cookie = `token=${token}; path=/; max-age=86400`; // 24小时
                sessionStorage.setItem('token', token);
                localStorage.setItem('token', token);
                
                showStatus('Token设置成功！');
                updateCurrentToken();
                document.getElementById('tokenInput').value = '';
            } catch (error) {
                showStatus(`设置失败: ${error.message}`, true);
            }
        }
        
        function clearToken() {
            document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
            sessionStorage.removeItem('token');
            localStorage.removeItem('token');
            showStatus('Token已清除');
            updateCurrentToken();
        }
        
        function useValidToken() {
            document.getElementById('tokenInput').value = validToken;
            setToken();
        }
        
        async function testToken() {
            const token = getToken();
            if (!token) {
                showStatus('请先设置token', true);
                return;
            }

            try {
                // 测试健康检查
                const healthResponse = await fetch('/fastapi/api/v1/iot/kb/health', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const healthResult = await healthResponse.json();
                if (healthResponse.ok && healthResult.code === 200) {
                    showStatus(`✅ 健康检查成功: ${healthResult.msg}`);

                    // 测试知识库列表
                    const kbResponse = await fetch('/fastapi/api/v1/iot/kb/datasets?page=1&page_size=3', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    const kbResult = await kbResponse.json();
                    if (kbResponse.ok && kbResult.code === 200) {
                        showStatus(`✅ 知识库测试成功！获取到 ${kbResult.data.length} 个知识库`);
                    } else {
                        showStatus(`❌ 知识库测试失败: ${kbResult.msg || '未知错误'}`, true);
                    }
                } else {
                    showStatus(`❌ 健康检查失败: ${healthResult.msg || '未知错误'}`, true);
                }
            } catch (error) {
                showStatus(`❌ 测试失败: ${error.message}`, true);
            }
        }
        
        // 页面加载时更新当前token状态
        updateCurrentToken();
    </script>
</body>
</html>
