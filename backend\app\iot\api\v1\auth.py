#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IoT 认证相关 API

提供 IoT 系统认证测试和用户信息获取接口
"""
from fastapi import APIRouter, Depends, Request

from backend.common.response.response_schema import ResponseModel, response_base
from backend.common.security.iot_permission import DependsIoTRBAC, IoTPermissions, require_iot_permission
from backend.common.security.jwt import DependsJwtAuth

router = APIRouter()


@router.post('/token/exchange', summary='Java系统token换取IoT token')
async def exchange_java_token_for_iot_token(request: Request) -> ResponseModel:
    """
    将Java系统的token转换为IoT系统的JWT token

    前端使用Java系统登录后，可以通过此接口获取IoT系统的JWT token
    """
    # 从请求头获取Java系统的token
    authorization = request.headers.get("Authorization")
    if not authorization:
        from backend.common.response.response_code import CustomResponse
        return response_base.fail(res=CustomResponse(400, "缺少Authorization头"))

    try:
        # 移除Bearer前缀
        token = authorization.replace("Bearer ", "").strip()

        # 使用IoT适配器验证Java token并获取用户信息
        from backend.common.security.iot_adapter import iot_adapter

        if not iot_adapter.is_enabled():
            from backend.common.response.response_code import CustomResponse
            return response_base.fail(res=CustomResponse(500, "IoT集成未启用"))

        # 验证Java token并获取用户信息
        user_info = await iot_adapter.authenticate_iot_token(token)

        # 构造IoT JWT token（包含login_user_key）
        import jwt
        import time
        from backend.core.conf import settings

        # 创建JWT payload
        payload = {
            'login_user_key': token,  # 使用Java系统的token作为login_user_key
            'exp': int(time.time()) + 86400,  # 24小时过期
            'iat': int(time.time()),
            'user_id': user_info.id,
            'username': user_info.username
        }

        # 生成JWT token
        iot_token = jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)

        return response_base.success(data={
            "iot_token": iot_token,
            "user_info": {
                "user_id": user_info.id,
                "username": user_info.username,
                "nickname": user_info.nickname
            }
        })

    except Exception as e:
        from backend.common.response.response_code import CustomResponse
        return response_base.fail(res=CustomResponse(500, f"Token转换失败: {str(e)}"))


@router.get('/user/info', summary='获取当前 IoT 用户信息', dependencies=[DependsJwtAuth])
async def get_iot_user_info(request: Request) -> ResponseModel:
    """
    获取当前 IoT 用户信息
    
    此接口用于测试 IoT 认证集成是否正常工作
    """
    user = request.user
    
    user_info = {
        'id': user.id,
        'username': user.username,
        'nickname': user.nickname,
        'email': user.email,
        'is_superuser': user.is_superuser,
        'is_staff': user.is_staff,
        'permissions': getattr(user, 'permissions', []),
        'roles': [{'id': role.id, 'name': role.name} for role in user.roles] if hasattr(user, 'roles') and user.roles else [],
        'dept': {
            'id': user.dept.id,
            'name': user.dept.name
        } if hasattr(user, 'dept') and user.dept else None
    }
    
    return response_base.success(data=user_info)


@router.get('/auth/test', summary='IoT 认证测试', dependencies=[DependsJwtAuth])
async def iot_auth_test(request: Request) -> ResponseModel:
    """
    IoT 认证测试接口
    
    用于验证 IoT token 是否能够正常通过认证
    """
    return await response_base.success(
        data={
            'message': 'IoT 认证测试成功',
            'user_id': request.user.id,
            'username': request.user.username,
            'timestamp': request.state.__dict__.get('request_time', 'unknown')
        }
    )


@router.get(
    '/permissions/test',
    summary='IoT 权限测试',
    dependencies=[
        Depends(require_iot_permission(IoTPermissions.USER_VIEW)),
        DependsIoTRBAC
    ]
)
async def iot_permission_test(request: Request) -> ResponseModel:
    """
    IoT 权限测试接口
    
    需要 'iot:user:view' 权限才能访问
    """
    user_permissions = getattr(request.user, 'permissions', [])
    
    return await response_base.success(
        data={
            'message': 'IoT 权限测试成功',
            'required_permission': IoTPermissions.USER_VIEW,
            'user_permissions': user_permissions,
            'has_permission': IoTPermissions.USER_VIEW in user_permissions or '*:*:*' in user_permissions
        }
    )


@router.get('/health', summary='IoT 健康检查')
async def iot_health_check() -> ResponseModel:
    """
    IoT 健康检查接口
    
    不需要认证，用于检查 IoT 模块是否正常运行
    """
    from backend.common.security.iot_adapter import iot_adapter
    from backend.core.conf import settings
    
    health_status = {
        'status': 'healthy',
        'iot_integration_enabled': settings.IOT_INTEGRATION_ENABLED,
        'iot_adapter_enabled': iot_adapter.is_enabled(),
        'iot_config_valid': iot_adapter.validate_config(),
        'timestamp': request.state.__dict__.get('request_time', 'unknown') if 'request' in locals() else 'unknown'
    }
    
    return await response_base.success(data=health_status)
