#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java RBAC适配器
实现Java权限系统到FastAPI RBAC的映射机制
"""
import re
from typing import List, Dict, Any, Optional
from fastapi import Request

from backend.common.log import log


class JavaRBACAdapter:
    """Java RBAC适配器"""
    
    @staticmethod
    async def verify_java_permission(request: Request, required_permission: str) -> bool:
        """
        验证Java系统权限
        
        :param request: FastAPI请求对象
        :param required_permission: 需要的权限标识
        :return: 是否有权限
        """
        try:
            user = request.user
            
            # 检查用户是否存在
            if not user:
                log.warning(f"Java权限验证: 用户不存在")
                return False
            
            # 超级管理员免校验
            if user.is_superuser:
                log.debug(f"Java权限验证: 超级管理员 {user.username} 免校验")
                return True
            
            # 获取用户权限列表
            user_permissions = JavaRBACAdapter._extract_user_permissions(user)
            
            log.debug(f"Java权限验证: 用户 {user.username} 权限列表: {user_permissions}")
            log.debug(f"Java权限验证: 需要权限: {required_permission}")
            
            # Java系统的通配符权限 "*:*:*" - 超级权限
            if "*:*:*" in user_permissions:
                log.debug(f"Java权限验证: 用户 {user.username} 拥有超级权限 *:*:*")
                return True
            
            # 精确匹配权限
            if required_permission in user_permissions:
                log.debug(f"Java权限验证: 用户 {user.username} 精确匹配权限 {required_permission}")
                return True
            
            # 模糊匹配权限（支持Java系统的权限格式）
            if JavaRBACAdapter._match_permission_pattern(user_permissions, required_permission):
                log.debug(f"Java权限验证: 用户 {user.username} 模糊匹配权限 {required_permission}")
                return True
            
            log.warning(f"Java权限验证: 用户 {user.username} 无权限 {required_permission}")
            return False
            
        except Exception as e:
            log.error(f"Java权限验证异常: {str(e)}")
            return False
    
    @staticmethod
    def _extract_user_permissions(user) -> List[str]:
        """
        提取用户权限列表
        
        :param user: FastAPI用户对象
        :return: 权限字符串列表
        """
        try:
            permissions = []
            
            # 从用户的permissions字段提取
            if hasattr(user, 'permissions') and user.permissions:
                for perm in user.permissions:
                    if isinstance(perm, dict) and 'name' in perm:
                        permissions.append(perm['name'])
                    elif isinstance(perm, str):
                        permissions.append(perm)
            
            # 从用户的roles中提取权限（如果有的话）
            if hasattr(user, 'roles') and user.roles:
                for role in user.roles:
                    if hasattr(role, 'menus') and role.menus:
                        for menu in role.menus:
                            if hasattr(menu, 'perms') and menu.perms:
                                # Java系统菜单权限通常用逗号分隔
                                role_perms = menu.perms.split(',')
                                permissions.extend([p.strip() for p in role_perms if p.strip()])
            
            return list(set(permissions))  # 去重
            
        except Exception as e:
            log.warning(f"提取用户权限失败: {str(e)}")
            return []
    
    @staticmethod
    def _match_permission_pattern(user_permissions: List[str], required_permission: str) -> bool:
        """
        匹配权限模式
        
        :param user_permissions: 用户权限列表
        :param required_permission: 需要的权限
        :return: 是否匹配
        """
        try:
            for user_perm in user_permissions:
                if JavaRBACAdapter._permission_matches(user_perm, required_permission):
                    return True
            return False
            
        except Exception as e:
            log.warning(f"权限模式匹配失败: {str(e)}")
            return False
    
    @staticmethod
    def _permission_matches(user_perm: str, required_perm: str) -> bool:
        """
        权限匹配逻辑
        
        :param user_perm: 用户拥有的权限
        :param required_perm: 需要的权限
        :return: 是否匹配
        """
        try:
            # 精确匹配
            if user_perm == required_perm:
                return True
            
            # 通配符匹配
            if user_perm.endswith('*'):
                # 支持通配符匹配，如 "sys:user:*" 匹配 "sys:user:add"
                prefix = user_perm[:-1]  # 移除最后的 *
                if required_perm.startswith(prefix):
                    return True
            
            # 支持正则表达式匹配（Java系统可能使用的高级权限格式）
            if '*' in user_perm or '?' in user_perm:
                # 将通配符转换为正则表达式
                pattern = user_perm.replace('*', '.*').replace('?', '.')
                if re.match(f'^{pattern}$', required_perm):
                    return True
            
            return False
            
        except Exception as e:
            log.warning(f"权限匹配逻辑异常: {str(e)}")
            return False
    
    @staticmethod
    def get_permission_hierarchy(permission: str) -> List[str]:
        """
        获取权限层次结构
        Java权限通常采用层次结构，如 "sys:user:add" 可以匹配 "sys:*" 或 "sys:user:*"
        
        :param permission: 权限字符串
        :return: 权限层次列表
        """
        try:
            if ':' not in permission:
                return [permission]
            
            parts = permission.split(':')
            hierarchies = []
            
            # 生成所有可能的层次权限
            for i in range(len(parts)):
                if i == len(parts) - 1:
                    # 最后一级，添加完整权限
                    hierarchies.append(':'.join(parts))
                else:
                    # 中间级别，添加通配符权限
                    hierarchies.append(':'.join(parts[:i+1]) + ':*')
            
            return hierarchies
            
        except Exception as e:
            log.warning(f"获取权限层次结构失败: {str(e)}")
            return [permission]
    
    @staticmethod
    def normalize_permission(permission: str) -> str:
        """
        标准化权限字符串
        
        :param permission: 原始权限字符串
        :return: 标准化后的权限字符串
        """
        try:
            if not permission:
                return ""
            
            # 移除多余的空格
            permission = permission.strip()
            
            # 统一分隔符（Java系统可能使用不同的分隔符）
            permission = permission.replace('/', ':').replace('.', ':')
            
            # 移除重复的分隔符
            while '::' in permission:
                permission = permission.replace('::', ':')
            
            return permission
            
        except Exception as e:
            log.warning(f"标准化权限字符串失败: {str(e)}")
            return permission
    
    @staticmethod
    def check_role_permissions(user, required_permission: str) -> bool:
        """
        检查角色权限
        
        :param user: 用户对象
        :param required_permission: 需要的权限
        :return: 是否有权限
        """
        try:
            if not hasattr(user, 'roles') or not user.roles:
                return False
            
            for role in user.roles:
                # 检查角色是否启用
                if hasattr(role, 'status') and role.status != 1:
                    continue
                
                # 检查角色的菜单权限
                if hasattr(role, 'menus') and role.menus:
                    for menu in role.menus:
                        if hasattr(menu, 'perms') and menu.perms:
                            menu_perms = menu.perms.split(',')
                            for perm in menu_perms:
                                perm = perm.strip()
                                if JavaRBACAdapter._permission_matches(perm, required_permission):
                                    return True
            
            return False
            
        except Exception as e:
            log.warning(f"检查角色权限失败: {str(e)}")
            return False
    
    @staticmethod
    def is_admin_user(user) -> bool:
        """
        判断是否为管理员用户
        
        :param user: 用户对象
        :return: 是否为管理员
        """
        try:
            # 检查超级用户标志
            if hasattr(user, 'is_superuser') and user.is_superuser:
                return True
            
            # 检查是否有超级权限
            user_permissions = JavaRBACAdapter._extract_user_permissions(user)
            if "*:*:*" in user_permissions:
                return True
            
            # 检查是否有管理员角色
            if hasattr(user, 'roles') and user.roles:
                for role in user.roles:
                    if hasattr(role, 'name') and 'admin' in role.name.lower():
                        return True
            
            return False
            
        except Exception as e:
            log.warning(f"判断管理员用户失败: {str(e)}")
            return False
    
    @staticmethod
    def get_user_permission_summary(user) -> Dict[str, Any]:
        """
        获取用户权限摘要（用于调试和日志）
        
        :param user: 用户对象
        :return: 权限摘要
        """
        try:
            summary = {
                'username': getattr(user, 'username', 'unknown'),
                'is_superuser': getattr(user, 'is_superuser', False),
                'is_admin': JavaRBACAdapter.is_admin_user(user),
                'permissions': JavaRBACAdapter._extract_user_permissions(user),
                'roles': []
            }
            
            if hasattr(user, 'roles') and user.roles:
                for role in user.roles:
                    role_info = {
                        'name': getattr(role, 'name', 'unknown'),
                        'status': getattr(role, 'status', 0)
                    }
                    summary['roles'].append(role_info)
            
            return summary
            
        except Exception as e:
            log.warning(f"获取用户权限摘要失败: {str(e)}")
            return {'error': str(e)}
