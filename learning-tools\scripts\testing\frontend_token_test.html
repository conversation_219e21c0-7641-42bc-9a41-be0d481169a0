<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端Token测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 前端Token测试工具</h1>
        <p>用于测试和验证前端JWT token与FastAPI后端的集成</p>

        <div class="section">
            <h3>1. 当前Token状态</h3>
            <button onclick="checkCurrentToken()">检查当前Token</button>
            <div id="token-status" class="result info">点击按钮检查当前token状态</div>
        </div>

        <div class="section">
            <h3>2. 设置测试Token</h3>
            <p>输入一个有效的JWT token（从Java后端获取或使用测试token）：</p>
            <textarea id="token-input" placeholder="粘贴JWT token到这里..."></textarea>
            <br><br>
            <button onclick="setTestToken()">设置Token到所有存储</button>
            <button onclick="generateTestToken()">生成测试Token</button>
            <button onclick="clearAllTokens()">清除所有Token</button>
            <div id="set-token-result" class="result"></div>
        </div>

        <div class="section">
            <h3>3. 测试API调用</h3>
            <button onclick="testHealthCheck()">测试健康检查</button>
            <button onclick="testKnowledgeBaseList()">测试知识库列表</button>
            <button onclick="testCreateKnowledgeBase()">测试创建知识库</button>
            <div id="api-test-result" class="result"></div>
        </div>

        <div class="section">
            <h3>4. Token解析工具</h3>
            <button onclick="parseCurrentToken()">解析当前Token</button>
            <div id="token-parse-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1/iot/kb';

        function displayResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
        }

        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        function checkCurrentToken() {
            const cookieToken = getCookie('token');
            const sessionToken = sessionStorage.getItem('token');
            const localToken = localStorage.getItem('token');

            let status = '📋 Token状态检查（按优先级）：\n\n';

            if (cookieToken) {
                status += `✅ Cookie Token: ${cookieToken.substring(0, 50)}...\n`;
            } else {
                status += `❌ Cookie Token: 未找到\n`;
            }

            if (sessionToken) {
                status += `✅ SessionStorage Token: ${sessionToken.substring(0, 50)}...\n`;
            } else {
                status += `❌ SessionStorage Token: 未找到\n`;
            }

            if (localToken) {
                status += `✅ LocalStorage Token: ${localToken.substring(0, 50)}...\n`;
            } else {
                status += `❌ LocalStorage Token: 未找到\n`;
            }

            // 按优先级选择token
            const finalToken = cookieToken || sessionToken || localToken;
            if (finalToken) {
                status += `\n🎯 将使用的Token: ${finalToken.startsWith('Bearer ') ? 'Bearer格式' : '需要添加Bearer前缀'}`;
                displayResult('token-status', status, 'success');
            } else {
                status += `\n⚠️  没有找到任何token，API调用将失败`;
                displayResult('token-status', status, 'warning');
            }
        }

        function setTestToken() {
            const tokenInput = document.getElementById('token-input').value.trim();
            if (!tokenInput) {
                displayResult('set-token-result', '❌ 请输入token', 'error');
                return;
            }

            // 确保token有Bearer前缀
            const token = tokenInput.startsWith('Bearer ') ? tokenInput : `Bearer ${tokenInput}`;

            // 设置到所有存储位置
            document.cookie = `token=${token}; path=/; max-age=86400`; // 24小时
            sessionStorage.setItem('token', token);
            localStorage.setItem('token', token);

            displayResult('set-token-result', `✅ Token已设置到所有存储位置\n\n${token.substring(0, 100)}...`, 'success');
        }

        function clearAllTokens() {
            // 清除所有存储位置的token
            document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
            sessionStorage.removeItem('token');
            localStorage.removeItem('token');

            displayResult('set-token-result', '✅ 已清除所有存储位置的token', 'success');
        }

        async function generateTestToken() {
            try {
                // 调用我们的测试脚本生成token
                const response = await fetch('/generate-test-token', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('token-input').value = data.token;
                    displayResult('set-token-result', `✅ 测试Token已生成\n\n${data.token}`, 'success');
                } else {
                    throw new Error('生成失败');
                }
            } catch (error) {
                // 如果无法调用后端，生成一个客户端测试token
                const testToken = generateClientTestToken();
                document.getElementById('token-input').value = testToken;
                displayResult('set-token-result', `✅ 客户端测试Token已生成\n\n${testToken}`, 'success');
            }
        }

        function generateClientTestToken() {
            // 简单的客户端token生成（仅用于测试）
            const header = btoa(JSON.stringify({alg: "HS512", typ: "JWT"}));
            const payload = btoa(JSON.stringify({
                login_user_key: generateUUID(),
                userid: "123",
                username: "test_user",
                iat: Math.floor(Date.now() / 1000),
                exp: Math.floor(Date.now() / 1000) + 86400,
                jti: generateUUID()
            }));
            const signature = "test_signature_" + Math.random().toString(36).substring(7);
            
            return `${header}.${payload}.${signature}`;
        }

        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        async function makeAPIRequest(endpoint, options = {}) {
            // 按优先级获取token
            const token = getCookie('token') || sessionStorage.getItem('token') || localStorage.getItem('token');
            
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            };
            
            if (token) {
                defaultOptions.headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    ...defaultOptions,
                    ...options
                });
                
                const data = await response.json();
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testHealthCheck() {
            displayResult('api-test-result', '🔄 正在测试健康检查...', 'info');
            
            const result = await makeAPIRequest('/health');
            
            if (result.success) {
                displayResult('api-test-result', 
                    `✅ 健康检查成功 (${result.status})\n\n${JSON.stringify(result.data, null, 2)}`, 
                    'success');
            } else {
                displayResult('api-test-result', 
                    `❌ 健康检查失败 (${result.status || 'Network Error'})\n\n${result.error || JSON.stringify(result.data, null, 2)}`, 
                    'error');
            }
        }

        async function testKnowledgeBaseList() {
            displayResult('api-test-result', '🔄 正在测试知识库列表...', 'info');
            
            const result = await makeAPIRequest('/knowledge-bases?page=1&page_size=5');
            
            if (result.success) {
                displayResult('api-test-result', 
                    `✅ 知识库列表获取成功 (${result.status})\n\n${JSON.stringify(result.data, null, 2)}`, 
                    'success');
            } else {
                displayResult('api-test-result', 
                    `❌ 知识库列表获取失败 (${result.status || 'Network Error'})\n\n${result.error || JSON.stringify(result.data, null, 2)}`, 
                    'error');
            }
        }

        async function testCreateKnowledgeBase() {
            displayResult('api-test-result', '🔄 正在测试创建知识库...', 'info');
            
            const kbData = {
                name: `Frontend_Test_${Date.now()}`,
                description: '前端测试创建的知识库',
                permission: 'me',
                chunk_method: 'naive'
            };
            
            const result = await makeAPIRequest('/knowledge-bases', {
                method: 'POST',
                body: JSON.stringify(kbData)
            });
            
            if (result.success) {
                displayResult('api-test-result', 
                    `✅ 知识库创建成功 (${result.status})\n\n${JSON.stringify(result.data, null, 2)}`, 
                    'success');
            } else {
                displayResult('api-test-result', 
                    `❌ 知识库创建失败 (${result.status || 'Network Error'})\n\n${result.error || JSON.stringify(result.data, null, 2)}`, 
                    'error');
            }
        }

        function parseCurrentToken() {
            const token = sessionStorage.getItem('token') || localStorage.getItem('token');
            
            if (!token) {
                displayResult('token-parse-result', '❌ 没有找到token', 'error');
                return;
            }
            
            try {
                // 移除Bearer前缀
                const cleanToken = token.replace('Bearer ', '');
                
                // 分割token
                const parts = cleanToken.split('.');
                if (parts.length !== 3) {
                    throw new Error('Token格式无效');
                }
                
                // 解析header和payload
                const header = JSON.parse(atob(parts[0]));
                const payload = JSON.parse(atob(parts[1]));
                
                // 检查过期时间
                const now = Math.floor(Date.now() / 1000);
                const isExpired = payload.exp && payload.exp < now;
                
                let result = '🔍 Token解析结果：\n\n';
                result += `Header:\n${JSON.stringify(header, null, 2)}\n\n`;
                result += `Payload:\n${JSON.stringify(payload, null, 2)}\n\n`;
                result += `状态: ${isExpired ? '❌ 已过期' : '✅ 有效'}\n`;
                
                if (payload.exp) {
                    const expireDate = new Date(payload.exp * 1000);
                    result += `过期时间: ${expireDate.toLocaleString()}\n`;
                }
                
                displayResult('token-parse-result', result, isExpired ? 'warning' : 'success');
                
            } catch (error) {
                displayResult('token-parse-result', `❌ Token解析失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查token状态
        window.onload = function() {
            checkCurrentToken();
        };
    </script>
</body>
</html>
