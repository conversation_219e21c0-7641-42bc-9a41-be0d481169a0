#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI与Java后端JWT认证集成测试
"""
import asyncio
import json
import aiohttp
import time
from typing import Dict, List, Optional

class IntegrationTester:
    """集成测试类"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.test_results = []
        
        # 测试用的token（admin用户）
        self.admin_token = "457b5f2c-6482-47c3-b22c-0cf6a5cf3508"
        self.admin_jwt_token = "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjQ1N2I1ZjJjLTY0ODItNDdjMy1iMjJjLTBjZjZhNWNmMzUwOCJ9.YEfuSy_rl2g4_9rbSd4A34qtatSWhOBfMe-Rzw0c3McGJQjMBIUs_9VWxEx8inrDU5asAtVTYTyl0m6bVmYfzw"
        
    async def make_request(self, method: str, endpoint: str, token: str = None, data: dict = None) -> Dict:
        """发起HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        headers = {"Content-Type": "application/json"}
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
            
        async with aiohttp.ClientSession() as session:
            try:
                if method.upper() == "GET":
                    async with session.get(url, headers=headers) as response:
                        result = await response.json()
                        return {
                            "status": response.status,
                            "data": result,
                            "success": response.status < 400
                        }
                elif method.upper() == "POST":
                    async with session.post(url, headers=headers, json=data) as response:
                        result = await response.json()
                        return {
                            "status": response.status,
                            "data": result,
                            "success": response.status < 400
                        }
            except Exception as e:
                return {
                    "status": 0,
                    "data": {"error": str(e)},
                    "success": False
                }
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": time.time()
        })
    
    async def test_health_check(self):
        """测试健康检查接口"""
        print("\n🔍 测试1: 健康检查接口")
        
        # 无token访问
        result = await self.make_request("GET", "/api/v1/iot/kb/health")
        self.log_test(
            "健康检查 (无token)", 
            result["success"] and result["data"]["code"] == 200,
            f"状态码: {result['status']}, 响应: {result['data'].get('msg', '')}"
        )
        
        # 有token访问
        result = await self.make_request("GET", "/api/v1/iot/kb/health", self.admin_jwt_token)
        self.log_test(
            "健康检查 (有token)", 
            result["success"] and result["data"]["code"] == 200,
            f"状态码: {result['status']}, 响应: {result['data'].get('msg', '')}"
        )
    
    async def test_authentication(self):
        """测试认证功能"""
        print("\n🔐 测试2: JWT认证功能")
        
        # 无token访问需要认证的接口
        result = await self.make_request("GET", "/api/v1/iot/kb/datasets")
        self.log_test(
            "无token访问受保护接口", 
            not result["success"] and result["status"] == 401,
            f"状态码: {result['status']} (期望401)"
        )
        
        # 无效token访问
        result = await self.make_request("GET", "/api/v1/iot/kb/datasets", "invalid_token")
        self.log_test(
            "无效token访问受保护接口", 
            not result["success"] and result["status"] == 401,
            f"状态码: {result['status']} (期望401)"
        )
        
        # 有效token访问
        result = await self.make_request("GET", "/api/v1/iot/kb/datasets", self.admin_jwt_token)
        self.log_test(
            "有效token访问受保护接口", 
            result["success"] and result["status"] == 200,
            f"状态码: {result['status']}, 数据: {len(result['data'].get('data', []))} 条记录"
        )
    
    async def test_permissions(self):
        """测试权限验证"""
        print("\n🛡️ 测试3: 权限验证功能")
        
        # 测试统计信息接口（仅需认证）
        result = await self.make_request("GET", "/api/v1/iot/kb/datasets/stats/summary", self.admin_jwt_token)
        self.log_test(
            "统计信息接口 (仅需认证)", 
            result["success"] and result["status"] == 200,
            f"状态码: {result['status']}, 统计数据: {result['data'].get('data', {})}"
        )
        
        # 测试认证测试接口（需要特定权限）
        result = await self.make_request("GET", "/api/v1/iot/kb/debug/auth-test", self.admin_jwt_token)
        self.log_test(
            "认证测试接口 (需要权限)", 
            result["success"] and result["status"] == 200,
            f"状态码: {result['status']}, 用户信息: {result['data'].get('data', {}).get('user', {}).get('username', '')}"
        )
    
    async def test_java_token_formats(self):
        """测试不同格式的Java token"""
        print("\n🔄 测试4: Java Token格式兼容性")
        
        # 测试UUID格式token
        result = await self.make_request("GET", "/api/v1/iot/kb/datasets", self.admin_token)
        self.log_test(
            "UUID格式token", 
            result["success"] and result["status"] == 200,
            f"状态码: {result['status']}"
        )
        
        # 测试JWT格式token
        result = await self.make_request("GET", "/api/v1/iot/kb/datasets", self.admin_jwt_token)
        self.log_test(
            "JWT格式token", 
            result["success"] and result["status"] == 200,
            f"状态码: {result['status']}"
        )
    
    async def test_error_handling(self):
        """测试错误处理"""
        print("\n⚠️ 测试5: 错误处理")
        
        # 测试不存在的接口
        result = await self.make_request("GET", "/api/v1/iot/kb/nonexistent", self.admin_jwt_token)
        self.log_test(
            "不存在的接口", 
            result["status"] == 404,
            f"状态码: {result['status']} (期望404)"
        )
        
        # 测试方法不允许
        result = await self.make_request("POST", "/api/v1/iot/kb/health", self.admin_jwt_token)
        self.log_test(
            "不允许的HTTP方法", 
            result["status"] == 405,
            f"状态码: {result['status']} (期望405)"
        )
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始FastAPI与Java后端JWT认证集成测试")
        print("=" * 60)
        
        start_time = time.time()
        
        # 运行所有测试
        await self.test_health_check()
        await self.test_authentication()
        await self.test_permissions()
        await self.test_java_token_formats()
        await self.test_error_handling()
        
        # 统计结果
        end_time = time.time()
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("📊 测试结果统计")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        print(f"耗时: {end_time-start_time:.2f}秒")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['details']}")
        
        return failed_tests == 0

async def main():
    """主函数"""
    tester = IntegrationTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！集成成功！")
        return 0
    else:
        print("\n💥 部分测试失败，请检查问题！")
        return 1

if __name__ == "__main__":
    import sys
    result = asyncio.run(main())
    sys.exit(result)
